# React Native WebView Integration - Summarized Assistant Messages

A simple integration that sends **summarized** assistant messages to your React Native app while preventing web speech API errors on mobile.

## What I Changed

**Only modified `src/hooks/use-speech-synthesis.ts`** to:

1. **Detect Mobile**: Check for `device=mobile` URL parameter
2. **Prevent Web Speech Errors**: Skip web speech API calls entirely on mobile
3. **Send Summarized Text**: Send the TTS-friendly summarized text to React Native instead

## How It Works

When `device=mobile` is in the URL:
- ✅ **No web speech API calls** - prevents "not supported" errors
- ✅ **Sends summarized text** - the same text that would be used for TTS
- ✅ **Automatic** - triggers whenever assistant responds

When `device=mobile` is NOT in the URL:
- ✅ **Normal web speech** - works exactly as before
- ✅ **No changes** - existing functionality preserved

## Message Format

Your React Native app receives:

```json
{
  "type": "ASSISTANT_MESSAGE",
  "payload": {
    "text": "Summarized, TTS-friendly version of assistant response",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "source": "summarized"
  }
}
```

## React Native Usage

```javascript
<WebView
  source={{ uri: 'https://your-app.com?device=mobile' }}
  onMessage={(event) => {
    const message = JSON.parse(event.nativeEvent.data);
    
    if (message.type === 'ASSISTANT_MESSAGE') {
      const { text, source } = message.payload;
      
      // This is the summarized, TTS-friendly text
      console.log('Summarized assistant message:', text);
      
      // Use native TTS or handle as needed
      Tts.speak(text);
    }
  }}
/>
```

## Key Benefits

1. **No Web Speech Errors**: Completely avoids "Web Speech API not supported" errors
2. **Summarized Content**: Gets the same processed text that would be used for TTS
3. **Simple**: Only one file modified (`use-speech-synthesis.ts`)
4. **Conditional**: Only works with `device=mobile` parameter
5. **Non-breaking**: Web functionality unchanged for regular browsers

## Testing

- Regular browser: `https://your-app.com` → Normal web speech
- Mobile WebView: `https://your-app.com?device=mobile` → No errors, sends to React Native

Perfect for React Native apps that want the assistant's summarized responses without web speech compatibility issues!

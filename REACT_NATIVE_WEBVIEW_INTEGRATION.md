# React Native WebView Integration - Assistant Messages

A simple integration that sends the assistant's last message to your React Native app via `window.ReactNativeWebView.postMessage()`.

## How It Works

1. **URL Parameter Detection**: The integration only activates when `device=mobile` is present in the URL
2. **Message Extraction**: Automatically extracts the last assistant message text from the chat
3. **React Native Messaging**: Sends the message to your React Native app via WebView postMessage

## Usage

### In Your React Native App

Add `?device=mobile` to your WebView URL:

```javascript
<WebView
  source={{ uri: 'https://your-web-app.com?device=mobile' }}
  onMessage={(event) => {
    const message = JSON.parse(event.nativeEvent.data);
    
    if (message.type === 'ASSISTANT_MESSAGE') {
      console.log('Assistant said:', message.payload.text);
      // Handle the assistant message in your React Native app
      // For example: trigger native TTS, show notification, etc.
    }
  }}
/>
```

### Message Format

When the assistant responds, your React Native app will receive:

```json
{
  "type": "ASSISTANT_MESSAGE",
  "payload": {
    "text": "The assistant's complete response text",
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

## Example React Native Handler

```javascript
const handleWebViewMessage = (event) => {
  try {
    const message = JSON.parse(event.nativeEvent.data);
    
    if (message.type === 'ASSISTANT_MESSAGE') {
      const { text, timestamp } = message.payload;
      
      // Example: Use native TTS
      Tts.speak(text);
      
      // Example: Show notification
      showNotification('Assistant Response', text);
      
      // Example: Store in state
      setLastAssistantMessage(text);
    }
  } catch (error) {
    console.error('Error parsing WebView message:', error);
  }
};
```

## Key Features

- **Simple**: Only sends assistant messages, no complex speech API integration
- **Conditional**: Only works when `device=mobile` is in the URL
- **Safe**: Gracefully handles cases where React Native WebView is not available
- **Non-breaking**: Doesn't interfere with existing web functionality
- **Automatic**: No manual triggering needed - works whenever assistant responds

## Testing

1. Open your web app in a regular browser: `https://your-app.com` - No messages sent
2. Open with mobile parameter: `https://your-app.com?device=mobile` - Messages sent to React Native

This approach avoids all the web speech API compatibility issues while giving you the assistant's text in your React Native app for native handling.

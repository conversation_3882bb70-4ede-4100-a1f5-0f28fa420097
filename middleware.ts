import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  /*
   * <PERSON><PERSON> starts the dev server and requires a 200 status to
   * begin the tests, so this ensures that the tests can start
   */

  if (pathname.startsWith("/ping")) {
    return new Response("pong", { status: 200 });
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/ping",
    /*
     * Match only the ping endpoint for <PERSON><PERSON> tests
     */
  ],
};

import {
  formattedTimeStamp,
  convertGMTDateToISTDate,
  extractBasedOnLanguage,
} from "./utils";
import language from "../language/language.json";
import KrushalLogo from "../logo";

interface BaseHTMLParams {
  activityDetails: any;
}
const userLanguage = "en";

// Safe access helper function
const safeGet = (obj: any, path: string, defaultValue: any = "") => {
  if (!obj) return defaultValue;

  const keys = path.split(".");
  let result = obj;

  for (const key of keys) {
    if (result === null || result === undefined || typeof result !== "object") {
      return defaultValue;
    }
    result = result[key];
  }

  return result !== null && result !== undefined ? result : defaultValue;
};

export function baseHTML({ activityDetails }: BaseHTMLParams) {
  // Ensure activityDetails exists
  const details = activityDetails || {};

  return `<!DOCTYPE html>
  <html xml:lang="en" lang="en">
  
  <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <title>Prescription</title>
      <style type="text/css">
          body{
            font-family: "Noto Sans", sans-serif;
          }
          @page {
            size: A4;
            margin: 0;
            margin-top: 20px;
            margin-bottom: 20px;
          }

          @page :first {
          
              margin-top: 0%;
          }
  
          .prescription .observation {
              page-break-inside: avoid
          }
  
          .prescription tr {
              page-break-inside: avoid;
              page-break-after: auto
          }
          .animal_table {
            width: 100%;
            margin-top: 50px;
            border: 1px solid black;
        }

        .animal_table>table {
            border-collapse: collapse;
            width: calc(100%);
            margin: 0 auto;
        }

        .animal_table>table>tbody>tr>td {
            width: calc(100%/3);
            padding-left: 15px;
        }
      </style>
  </head>
  
  <body>
      <div class="prescription" style="margin-right:0pt;margin-left:0pt">
          
      <header style='display:flex; justify-content:center; align-items:center; height:fit-content; margin-top:10px;' ><img src="${
        KrushalLogo?.[userLanguage] || ""
      }" height="40px" /> </header>
          
          <table style="border-collapse: collapse; width:100%; margin:5px 0 20px 0!important">
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                      ${extractBasedOnLanguage(
                        safeGet(language, "templateLabels.CustomerComplaintID"),
                        userLanguage
                      )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "ticket_number")}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.Date"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "activity_date") &&
                        safeGet(details, "activity_date").length > 0
                          ? formattedTimeStamp(
                              convertGMTDateToISTDate(
                                safeGet(details, "activity_date")
                              )
                            )
                          : ""
                      }
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.TimeOfComplaint"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      16:19
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.FarmerName"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "customer_name_l10n")
                          ? extractBasedOnLanguage(
                              safeGet(details, "customer_name_l10n"),
                              userLanguage
                            ) || ""
                          : ""
                      }
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.FarmerPhoneNo"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "farmer_contact")}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.FarmerID"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "customer_visual_id")}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.AnimalName"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "animal_name")}
                  </td>
              </tr>
  
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.AnimalTagNo"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "ear_tag")}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.AnimalReferenceNumber"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${safeGet(details, "animal_visual_id")}
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.village"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "village_name_l10n")
                          ? extractBasedOnLanguage(
                              safeGet(details, "village_name_l10n"),
                              userLanguage
                            ) || ""
                          : ""
                      }
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.DrOrLSS"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "paravet_name")
                          ? extractBasedOnLanguage(
                              safeGet(details, "paravet_name"),
                              userLanguage
                            ) || ""
                          : ""
                      }
                  </td>
              </tr>
             
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                  ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.VeterinaryOfficer"),
                    userLanguage
                  )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "vet_name")
                          ? extractBasedOnLanguage(
                              safeGet(details, "vet_name"),
                              userLanguage
                            ) || ""
                          : ""
                      }
                  </td>
              </tr>
              
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                  ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.ActivityOrComplaint"),
                    userLanguage
                  )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "activity_name_l10n")
                          ? extractBasedOnLanguage(
                              safeGet(details, "activity_name_l10n"),
                              userLanguage
                            ) || ""
                          : ""
                      }
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.FollowUpScheduledOn"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "followUpDateTime")
                          ? formattedTimeStamp(
                              convertGMTDateToISTDate(
                                safeGet(details, "followUpDateTime")
                              )
                            )
                          : ""
                      }
                  </td>
              </tr>
              <tr>
                  <th style='border: thin solid black; width:30%; text-align:left;padding:5px; padding-left:10px;background-color:white;color:black;' >
                    ${extractBasedOnLanguage(
                      safeGet(language, "templateLabels.ResponseToTreatment"),
                      userLanguage
                    )}
                  </th>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>
                      ${
                        safeGet(details, "response_to_treatment") &&
                        Array.isArray(
                          safeGet(details, "response_to_treatment")
                        ) &&
                        safeGet(details, "response_to_treatment").length > 0
                          ? safeGet(details, "response_to_treatment")[0]
                              ?.rtt_name || ""
                          : ""
                      }
                  </td>
              </tr>
  
          </table>



          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
            <tr>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'>${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.CaseDescription"),
                  userLanguage
                )}</th>
            </tr>
                 ${
                   safeGet(details, "observationNotes") &&
                   Array.isArray(safeGet(details, "observationNotes")) &&
                   safeGet(details, "observationNotes").length > 0
                     ? `

                 ${safeGet(details, "observationNotes")
                   .map((data: any, index: number) => {
                     return `<tr style="">
                     <td  style='border: thin solid black;padding:5px; padding-left:10px;'>

                         ${
                           safeGet(data, "note")
                             ? extractBasedOnLanguage(
                                 safeGet(data, "note"),
                                 userLanguage
                               ) || ""
                             : ""
                         }

                     </td>
                 </tr>`;
                   })
                   .join("")}
                 `
                     : `<tr>
                          <td  style='border: thin solid black;padding:5px; padding-left:10px;'>  &#20;  </td>
                        </tr>`
                 }
          </table>

  
  
          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='3' style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'> ${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.Observation"),
                  userLanguage
                )} </th>
              </tr>
              <tr>
                  <th style='border: thin solid black;  text-align:left;padding:5px; padding-left:10px;width:10%;background-color:#FF5722; color:white;' >  ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.SerialNumber"),
                    userLanguage
                  )}  </th>
                  <th style='border: thin solid black; width:50%; text-align:left;padding:5px; padding-left:10px;background-color:#FF5722; color:white;' >  ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Name"),
                    userLanguage
                  )}    </th>
                  <th style='border: thin solid black; text-align:left;padding:5px; padding-left:10px;background-color:#FF5722; color:white;' > ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Value"),
                    userLanguage
                  )}</th>
              </tr>
              ${
                safeGet(details, "observation") &&
                Array.isArray(safeGet(details, "observation")) &&
                safeGet(details, "observation").length > 0
                  ? safeGet(details, "observation")
                      .map((data: any, index: number) => {
                        return `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${
                    index + 1
                  } </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${safeGet(
                    data,
                    "obs_name"
                  )} </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> ${safeGet(
                    data,
                    "obs_val"
                  )} </td>
              </tr>`;
                      })
                      .join(" ")
                  : `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td>
                  </tr>`
              }
          </table>
  



  
          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='2'style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px;background-color:#512DA8;color:white'> ${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.Diagnosis"),
                  userLanguage
                )} </th>
              </tr>
              <tr>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; width:10%;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.SerialNumber"),
                  userLanguage
                )}  </th>
                <th style='border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.Name"),
                  userLanguage
                )} </th>
              </tr>
              ${
                safeGet(details, "diagnosis") &&
                Array.isArray(safeGet(details, "diagnosis")) &&
                safeGet(details, "diagnosis").length > 0
                  ? safeGet(details, "diagnosis")
                      .map((data: any, index: number) => {
                        return `
              <tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>  ${
                    index + 1
                  }                            </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'>  ${safeGet(
                    data,
                    "diag_name"
                  )} </td>
              </tr>
              `;
                      })
                      .join(" ")
                  : `<tr>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> &#20; </td>
                  <td style='border: thin solid black;padding:5px; padding-left:10px;'> &#20; </td>
                  </tr>`
              }
  
          </table>




          <table style="border-collapse: collapse; width:100%; margin:20px 0 20px 0;">
              <tr>
                <th colspan='7'style='border: thin solid black;text-align:center;padding:5px; padding-left:10px; font-size:17px; background-color:#512DA8;color:white'>${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.Prescription"),
                  userLanguage
                )} </th>
              </tr>
              <tr>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;width:10%'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.SerialNumber"),
                    userLanguage
                  )} </th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#FF5722; color:white;width:30%'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.MedicineName"),
                    userLanguage
                  )}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Dose"),
                    userLanguage
                  )}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Unit"),
                    userLanguage
                  )}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Frequnecy"),
                    userLanguage
                  )}</th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;width:20%;background-color:#FF5722; color:white;'> ${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Route"),
                    userLanguage
                  )} </th>
                  <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px;padding-left:10px;background-color:#FF5722; color:white;'>${extractBasedOnLanguage(
                    safeGet(language, "templateLabels.Price"),
                    userLanguage
                  )}</th>
              </tr>
              ${
                safeGet(details, "prescription") &&
                Array.isArray(safeGet(details, "prescription")) &&
                safeGet(details, "prescription").length > 0
                  ? safeGet(details, "prescription")
                      .map((data: any, index: number) => {
                        return `
                    <tr>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${
                        index + 1
                      }</td>
                      <td style='font-size:13px;text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${safeGet(
                        data,
                        "prescription_name"
                      )}</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${
                        safeGet(data, "prescription_value")
                          ? parseFloat(
                              safeGet(data, "prescription_value")
                            ).toFixed(2)
                          : "-"
                      }</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${
                        safeGet(data, "prescription_details.unit") || "-"
                      }</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${
                        safeGet(data, "prescription_details.frequency") || "-"
                      }</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>${
                        safeGet(data, "prescription_details.route_name") || "-"
                      }</td>
                      <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;'>&#x20B9;${
                        safeGet(data, "prescription_details.mrp")
                          ? parseFloat(
                              safeGet(data, "prescription_details.mrp")
                            ).toFixed(2)
                          : "0"
                      }</td>
                    </tr>
                  `;
                      })
                      .join("")
                  : ""
              }

             <tr>
                <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;background-color:white' colspan="5">${extractBasedOnLanguage(
                  safeGet(language, "templateLabels.VetConsultationFee"),
                  userLanguage
                )}</td>
                <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="2">&#x20B9;${
                  safeGet(details, "vet_consultant_cost") === undefined ||
                  safeGet(details, "vet_consultant_cost") === null ||
                  safeGet(details, "vet_consultant_cost") === ""
                    ? 0
                    : safeGet(details, "vet_consultant_cost")
                }</td>
            </tr>
            <tr>
              <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="5">${extractBasedOnLanguage(
                safeGet(language, "templateLabels.TotalPrice"),
                userLanguage
              )}</td>
              <td style='text-align:center;border: thin solid black;padding:5px; padding-left:10px;' colspan="2">&#x20B9;${safeGet(
                details,
                "total_price",
                "0"
              )}</td>
            </tr>

          </table>



          <table style="border-collapse: collapse; width:100%; break-inside:avoid; margin:20px 0 20px 0;">
            <tr>
              <th style='font-size:15px;border: thin solid black;text-align:center;padding:5px; padding-left:10px;background-color:#512DA8;color:white; color:white'>${extractBasedOnLanguage(
                safeGet(language, "templateLabels.VetAdvisoryNotes"),
                userLanguage
              )}</th>
            </tr>
            ${
              safeGet(details, "prescriptionNotes") &&
              Array.isArray(safeGet(details, "prescriptionNotes")) &&
              safeGet(details, "prescriptionNotes").length > 0
                ? safeGet(details, "prescriptionNotes").map(
                    (data: any, index: number) => {
                      return `<tr><td style='border: thin solid black;padding:5px; padding-left:10px;'>${
                        safeGet(data, "note")
                          ? extractBasedOnLanguage(
                              safeGet(data, "note"),
                              userLanguage
                            ) || ""
                          : ""
                      }</td></tr>`;
                    }
                  )
                : `<tr><td style='border: thin solid black;padding:5px; padding-left:10px;'>&#20;</td></tr>`
            }
          </table>

  </div>
  </body>
  
</html>
  `;
}

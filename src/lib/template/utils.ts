

export function formattedTimeStamp(timestamp: string | number | Date): string {
	const date = new Date(timestamp);

	const options: Intl.DateTimeFormatOptions = {
		day: "2-digit",
		month: "short",
		year: "numeric",
		hour: "numeric",
		minute: "numeric",
		hour12: true,
		timeZone: "UTC"
	};

	const formattedDate = date.toLocaleString("en-GB", options);
	return formattedDate;
}

export const convertGMTDateToISTDate = (date :any) => {
  let istOffset = 5.5
  const givenDate = new Date(date)
  const gmtDate = new Date(givenDate.getTime() + istOffset * 60 * 60 * 1000)

  return gmtDate
}


export function extractBasedOnLanguage(e : any, t : any) {
  if (t === undefined) {
    t = "en";
  }
  if (e === null || e === undefined) {
    return "";
  }
  if (typeof e === "object") {
    if (e.ul !== undefined && e.ul !== null) {
      return e.ul;
    } else if (e[t] !== undefined && e[t] !== null) {
      return e[t];
    } else if (e.en !== undefined && e.en !== null) {
      return e.en;
    } else {
      return "";
    }
  } else {
    return e;
  }
}
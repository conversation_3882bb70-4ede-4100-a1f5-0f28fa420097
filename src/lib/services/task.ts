import { RequestOptions } from "@/types/RequestOption";
import axios from "axios";

const headers = {
  "Content-Type": "application/json",
};
export const getTaskBySrNumber = async (
  SrNumber: string,
  options: RequestOptions
) => {
  try {
    const response = await axios.post(
      `${options.baseUrl}/activity/task-list`,
      { page_number: 1, page_limit: 100, f_complaint_number: SrNumber },
      { headers: options.headers }
    );

    if (response.status !== 200 && response.status !== 201) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = response.data;
    if (data.success === false || data.success === -1) {
      throw new Error("Operation failed: Server returned success false");
    }

    // console.log("activity task", data.result);
    return data.result;
  } catch (error) {
    return { error };
  }
};
export const getAdditionalTaskDetails = async (
  careCalendarId: string,
  options: RequestOptions
) => {
  try {
    // console.log("careCalendarId", careCalendarId);
    const response = await axios.get(
      `${options.baseUrl}/activity/by-id?calendar_id=${careCalendarId}`,
      { headers: options.headers }
    );

    if (response.status !== 200) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = response.data;
    if (data.success === false || data.success === -1) {
      throw new Error("Operation failed: Server returned success false");
    }

    // console.log("additional task details", data);
    return data;
  } catch (error) {
    console.log("task list error", error);
    return { error };
  }
};

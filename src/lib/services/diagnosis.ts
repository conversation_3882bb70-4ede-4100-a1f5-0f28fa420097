import axios from "axios";
import { diagnosisDetailsSchema } from "../ai/tools/schema/diagnosis-details";
import { z } from "zod";
import { RequestOptions } from "@/types/RequestOption";

const headers = {
  "Content-Type": "application/json",
  Origin: "https://apps3.krushal.in",
  Token: process.env.Token || "",
};
export const diagnosisList = async (
  careCalendarId: string,
  options: RequestOptions
) => {
  try {
    console.log("careCalendarId", careCalendarId);
    const response = await axios.get(
      `${options.baseUrl}/activity/diagnosis/${careCalendarId}`,
      { headers: options.headers }
    );

    const diagnosisList = response.data.data.options.diagnosis;
    const diagnosisListArray = diagnosisList.map((diagnosis: any) => ({
      id: diagnosis.reference_id,
      name: diagnosis.reference_name_l10n.en,
    }));

    return diagnosisListArray;
  } catch (error) {
    console.log("task list error", error);
    return error;
  }
};

export type updateDiagnosisParams = z.infer<typeof diagnosisDetailsSchema>;

export const updateDiagnosis = async (
  params: updateDiagnosisParams,
  options: RequestOptions
) => {
  try {
    // const getDiagnosisByActivityId = await getDiagnosisById(
    //   params.careCalendarId,
    //   options
    // );
    // const previousDiagnosis =
    //   getDiagnosisByActivityId.length > 0 ? getDiagnosisByActivityId : [];

    const payload = {
      diagnosis: [
        ...params.diagnosis.map((diagnosis: any) => ({
          value: diagnosis.id,
        })),
      ],
      activity_id: params.careCalendarId,
      response_to_treatment: [{ value: 1008000003 }],
    };

    const response = await fetch(`${options.baseUrl}/v2/tasks/diagnosis`, {
      method: "PUT",
      headers: options.headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    if (data.success === false || data.success === -1) {
      throw new Error("Operation failed: Server returned success false");
    }

    return "success";
  } catch (error) {
    console.log("updateDiagnosis error", error);
    return "failed to update";
  }
};

export const getDiagnosisById = async (
  activityId: string,
  options: RequestOptions
) => {
  try {
    const url = `${options.baseUrl}/activity/diagnosis/${activityId}`;

    const response = await axios.get(url, { headers: options.headers });

    return response.data?.data?.selected?.diagnosis;
  } catch (error) {
    console.log("getDiagnosisById error", error);
    return "failed to get";
  }
};

import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from "ai";
import { groq } from "@ai-sdk/groq";
import { mistral } from "@ai-sdk/mistral";
import { isTestEnvironment } from "../constants";
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from "./models.test";
import { openai } from "@ai-sdk/openai";

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        "chat-model": chatModel,
        "chat-model-reasoning": reasoningModel,
        "title-model": titleModel,
        "artifact-model": artifactModel,
        "summarize-model": chatModel,
      },
    })
  : customProvider({
      languageModels: {
        "chat-model": openai("gpt-4o-mini-2024-07-18"),
        "chat-model-reasoning": wrapLanguageModel({
          model: mistral("mistral-large-latest"),
          middleware: extractReasoningMiddleware({ tagName: "think" }),
        }),
        "title-model": groq("meta-llama/llama-4-maverick-17b-128e-instruct"),
        "artifact-model": mistral("mistral-large-latest"),
        "summarize-model": groq(
          "meta-llama/llama-4-maverick-17b-128e-instruct"
        ),
      },
    });
// openai("gpt-4o-mini-2024-07-18"),

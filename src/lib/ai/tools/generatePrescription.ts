import { tool } from "ai";
import { z } from "zod";
import { medicinesDetailsSchema } from "./schema/medicines-details";
import { diagnosisDetailsSchema } from "./schema/diagnosis-details";

import { updateDiagnosis } from "@/lib/services/diagnosis";
import { updateMedicines } from "@/lib/services/medicines";
import { RequestOptions } from "@/types/RequestOption";

import { updateObservation } from "@/lib/services/observation";
import { observationDetailsSchema } from "./schema/observation-details";
import { advisoryNotesSchema } from "./zod-schema/zod-schema";
import { downloadPDFDetailsSchema } from "./schema/download-pdf-details";

export const generatePrescription = (options: RequestOptions) =>
  tool({
    description:
      "Create prescription for a bovine based on details but you need observation, diagnosis , medication and vet consultant type before generating. create a summary for this prescription. from all the details given it should be in text",
    parameters: z.object({
      observation: observationDetailsSchema,
      diagnosis: diagnosisDetailsSchema,
      medication: medicinesDetailsSchema,
      advisoryNotes: advisoryNotesSchema,
      downloadPDF: downloadPDFDetailsSchema,
    }),

    execute: async ({
      // complaintDetails,
      observation,
      advisoryNotes,
      diagnosis,
      medication,
      downloadPDF,
    }) => {
      console.log(
        "generatePrescription",
        observation,
        medication,
        diagnosis,
        advisoryNotes,
        downloadPDF
      );
      if (!diagnosis) {
        return "Required Diagnosis details,  ask for diagnosis";
      }

      if (!medication) {
        return "Required Medication details,  ask for medication";
      }

      try {
        // clean up old stuff diagnosis and medicines,
        await updateDiagnosis(
          {
            careCalendarId: diagnosis.careCalendarId,
            diagnosis: [],
          },
          options
        );
        await updateMedicines(
          {
            careCalendarId: medication.careCalendarId,
            medicines: [],
            followUpRequired: medication.followUpRequired,
            followUpDate: medication.followUpDate,
            followUpDateGMT: medication.followUpDateGMT,
            advisoryNotes: advisoryNotes,
          },
          options
        );
        console.log("clear");
        await updateDiagnosis(diagnosis, options);
        console.log("Updated Diagnosis");

        await updateMedicines(
          {
            ...medication,
            advisoryNotes: advisoryNotes,
          },
          options
        )
          .then((res) => console.log("Updated Medication", res))
          .catch((err) => console.log("Error in updating Medication", err));

        const observationData = {
          ...observation,
          weight: observation.weight === undefined ? null : observation.weight,
          temperature:
            observation.temperature === undefined
              ? null
              : observation.temperature,
          num_calvings:
            observation.numCalvings === undefined ? 0 : observation.numCalvings,
          months_pregnant:
            observation.monthsPregnant === undefined
              ? 0
              : observation.monthsPregnant,
          avg_lpd:
            observation.averageLPD === undefined ? 0 : observation.averageLPD,
        };

        await updateObservation(observationData, options);
        console.log("Updated Observation", observationData);

        return {
          message: "Prescription completed and generated",
          data: downloadPDF,
        };
        // return "success";
      } catch (err) {
        console.log("Error in updating diagnosis", err);
        return "Error in updating diagnosis";
      }
    },
  });

import { tool } from "ai"
import { z } from "zod"
import Fuse from "fuse.js"

const medicineRouteList = [
  { short_name: "oral", name: "Oral", id: 1009000009 },
  { short_name: "iv", name: "Intravenous", id: 1009000007 },
  { short_name: "im", name: "Intramuscular", id: 1009000003 },
  { short_name: "sc", name: "Subcutaneous", id: 1009000010 },
  { short_name: "epidural", name: "Epidural", id: 1009000001 },
  { short_name: "intrauterine", name: "Intrauterine", id: 1009000006 },
  { short_name: "intramammary", name: "Intramammary", id: 1009000004 },
  { short_name: "intraparetoneal", name: "Intraperitoneal", id: 1009000005 },
  { short_name: "local", name: "Local", id: 1009000008 },
]

// Configure Fuse.js for fuzzy searching
const fuseOptions = {
  keys: ["short_name", "name"],
  threshold: 0.3, // Lower threshold = more strict matching
  includeScore: true,
  includeMatches: true,
}

const fuse = new Fuse(medicineRouteList, fuseOptions)

export const administrationCheckTool = () =>
  tool({
    description:
      "Tool to check if a route of administration is available in the medicine route list and validate dosage frequency",
    parameters: z.object({
      route: z.string().describe("The route of administration to check (e.g., 'oral', 'iv', 'intramuscular')"),
      dosage: z.string().optional().describe("The dosage information (e.g., '2.5 ml per 50 kg')"),
      frequency: z
        .enum(["SID", "BID", "TID"])
        .optional()
        .describe("Frequency of administration - SID (once daily), BID (twice daily), or TID (three times daily)"),
    }),
    execute: async ({ route, dosage, frequency }) => {

        console.log("administrationCheckTool", route, dosage, frequency)
      // Search for the route using Fuse.js
      const searchResults = fuse.search(route)

      let routeMatch = null
      let isExactMatch = false

      if (searchResults.length > 0) {
        const bestMatch = searchResults[0]
        routeMatch = bestMatch.item

        // Check if it's an exact match (score closer to 0 is better)
        isExactMatch = bestMatch.score !== undefined && bestMatch.score < 0.1
      }

      // Validate frequency if provided
      const validFrequencies = ["SID", "BID", "TID"]
      const isValidFrequency = frequency ? validFrequencies.includes(frequency) : true

      return {
        routeFound: !!routeMatch,
        routeMatch: routeMatch
          ? {
              id: routeMatch.id,
              name: routeMatch.name,
              shortName: routeMatch.short_name,
            }
          : null,
        isExactMatch,
        searchScore: searchResults.length > 0 ? searchResults[0].score : null,
        dosage: dosage || null,
        frequency: frequency || null,
        isValidFrequency,
        frequencyOptions: validFrequencies,
        suggestions: searchResults.slice(0, 3).map((result) => ({
          name: result.item.name,
          shortName: result.item.short_name,
          score: result.score,
        })),
        availableRoutes: medicineRouteList.map((route) => ({
          name: route.name,
          shortName: route.short_name,
        })),
      }
    },
  })
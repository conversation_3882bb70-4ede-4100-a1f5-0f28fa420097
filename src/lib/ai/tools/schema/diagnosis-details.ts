import { z } from "zod";
import { careCalenderIdSchema, diagnosisNameSchema } from "../zod-schema/zod-schema";

export const diagnosisDetailsSchema = z.object({
  careCalendarId: z
    .string()
    .describe(
      "care calendar id is required get this from getComplaintDetailsById tool"
    )
    .min(1, "care_calendar_id is required"),
  diagnosis: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "diagnosis id is required"), z.number()])
          .describe("diagnosis id"),
        name: z
          .string()
          .min(1, "diagnosis name is required")
          .describe("diagnosis name"),
      })
    )
    .min(1, "diagnosis is required")
    .describe("diagnosis is required"),
});

export const checkDiagnosisDetailsSchema = z.object({
  diagnosisName : diagnosisNameSchema.describe("diagnosis name is required to check with the list"),
  careCalendarId: careCalenderIdSchema.describe(
          "care calendar id is required to get the diagnosis list. this is available from tool response getComplaintDetailsById"
        ),
})

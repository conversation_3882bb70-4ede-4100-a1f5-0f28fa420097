import z from 'zod'
import { careCalenderIdSchema, temperatureSchema, weightSchema , numCalvingsSchema , monthsPregnantSchema, averageLPDSchema} from "../zod-schema/zod-schema";

export const observationDetailsSchema = z.object({
  weight: weightSchema,
  temperature: temperatureSchema,
  numCalvings  : numCalvingsSchema,
  monthsPregnant : monthsPregnantSchema,
  averageLPD : averageLPDSchema,
  careCalendarId: careCalenderIdSchema,
}).describe("The observation of the bovine in parameter (Vital informations)");

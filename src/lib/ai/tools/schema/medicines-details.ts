import { z } from "zod";
import { careCalenderIdSchema, medicineNameSchema , medicineIdSchema, followUpRequiredSchema, followUpDateSchema, followUpDateGMTSchema} from "../zod-schema/zod-schema";

export const medicinesDetailsSchema = z.object({
  careCalendarId: careCalenderIdSchema,
  medicines: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "medicine id is required"), z.number()])
          .describe("medicine id take it from checkMedicine tool response"),
        name: z.string().optional().describe("medicine name is required"),
        dosage: z.number().describe("dosage of the medicine"),
        unit: z.string().describe("unit of the medicine dosage"),
        administrationRouteId: z
          .union([
            z.string().min(1, "route of the medicine is required"),
            z.number(),
          ])
          .describe("route of the medicine"),
        frequency: z
          .enum(["SID", "BID", "TID"])
          .describe("frequency of the medicine"),
      })
    )
    .min(1, "At least one medicine is required")
    .describe(
      "Array of medicine objects after based on checkMedicine tool, checkMedicineRoute tool"
    ),

  followUpRequired: followUpRequiredSchema,
  followUpDate: followUpDateSchema,
  followUpDateGMT: followUpDateGMTSchema,
});


export const checkMedicineDetailsSchema = z.object({
      medicines: z
        .array(
          z.object({
            name: medicineNameSchema,
          })
        )
        .min(1, "At least one medicine is required")
        .describe("Array of medicine objects to update"),
    })
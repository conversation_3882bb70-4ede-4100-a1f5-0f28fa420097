import z from 'zod';

export const careCalenderIdSchema = z.string().describe("Care calendar id").min(1, "Care calendar id is required");
export const diagnosisNameSchema = z.string().describe("diagnosis name is required to check with the list");
export const caseDescriptionSchema = z.string().min(1, "case description is required");
export const animalIdSchema = z.string().describe("animal id is required").min(1, "animal id is required");
export const administrationRouteNameSchema = z.string().describe("route name is required to check with the list");
export const followUpRequiredSchema = z.boolean().describe("Indicates if a follow-up is required");
export const followUpDateSchema = z.string().describe("Date of the follow up in ISO format required time also");
export const followUpDateGMTSchema = z.string().describe("Convert the follow up date to GMT format");
export const advisoryNotesSchema = z.string().describe("advisory notes for the prescription");
export const weightSchema = z.number().describe("Weight of the bovine from observation");
export const temperatureSchema = z.number().describe("Temperature of the bovine from observation");
export const farmerIdSchema = z.string().describe("farmer id is required").min(1, "farmer id is required");
export const medicineIdSchema = z.string().describe("medicine id is required").min(1, "medicine id is required");
export const medicineNameSchema = z.string().describe("medicine name is required");
export const dosageSchema = z.number().describe("dosage of the medicine");
export const unitSchema = z.string().describe("unit of the medicine dosage");
export const administrationRouteIdSchema = z.string().describe("route of the medicine");
export const frequencySchema = z.string().describe("frequency of the medicine");
export const diagnosesSchema = z.array(z.string()).min(1, "At least one diagnosis is required").max(5, "Maximum 5 diagnoses allowed").describe("Array of diagnoses for the case")
export const diagnosisIdSchema = z.array(z.number()).describe("diagnosis id is required and take it from the tool response checkDiagnosis")
export const numCalvingsSchema = z.number().describe("Number of calvings - take value from vital context").optional();
export const monthsPregnantSchema = z.number().describe("Months pregnant - take value from vital context").optional();
export const averageLPDSchema = z.number().describe("Average litres per day (LPD) - take value from vital context").optional();


export const observationSchema = z.object({
  weight: weightSchema,
  temperature: temperatureSchema,
  careCalendarId: careCalenderIdSchema,
}).describe("The observation of the bovine in parameter  ");

export const diagnosisSchema = z.object({
  careCalendarId: careCalenderIdSchema,
  diagnosis: z.array(z.object({
    id: z.string().describe("diagnosis id"),
    name: z.string().describe("diagnosis name"),

  })).describe("diagnosis is required"),
}).describe("diagnosis is required");

export const medicationSchema = z.object({
  careCalendarId: careCalenderIdSchema,
  medicines: z.array(z.object({
    id: z.string().describe("medicine id"),
    name: z.string().describe("medicine name"),
    dosage: z.number().describe("dosage of the medicine"),
    unit: z.string().describe("unit of the medicine dosage"),
    administrationRouteId: z.string().describe("route of the medicine"),
    frequency: z.string().describe("frequency of the medicine"),
  })).describe("medicines is required"),
}).describe("medication is required");

export const generatePrescriptionSchema = z.object({
  observation: observationSchema,
  diagnosis: diagnosisSchema,
  medication: medicationSchema,
  advisoryNotes: advisoryNotesSchema,
  downlaodPDF: z.object({
    farmerId: farmerIdSchema,
    careCalendarId: careCalenderIdSchema,
  }),
}).describe("generatePrescriptionSchema is required");

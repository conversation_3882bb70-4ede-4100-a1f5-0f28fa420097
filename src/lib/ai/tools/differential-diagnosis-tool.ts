import { RequestOptions } from "@/types/RequestOption";
import { tool } from "ai";
import { z } from "zod";

// Types for better type safety
interface DiagnosisResult {
  confidence_score: number;
  description: string;
  name: string;
  notes: string;
  symptoms: string;
  treatment: string;
}

interface DiagnosticSession {
  sessionId: string;
  isCompleted: boolean;
  currentQuestion?: string;
  questionNumber?: number;
  maxQuestions?: number;
  lastUpdated: Date;
}

// In-memory session storage (replace with database in production)
const activeSessions = new Map<string, DiagnosticSession>();

// Session cleanup - remove sessions older than 1 hour
const cleanupSessions = () => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  for (const [sessionId, session] of activeSessions.entries()) {
    if (session.lastUpdated < oneHourAgo) {
      activeSessions.delete(sessionId);
    }
  }
};

// Clean up sessions every 30 minutes
setInterval(cleanupSessions, 30 * 60 * 1000);

export const differentialDiagnosisTool = (options: RequestOptions) =>
  tool({
    description: `Perform differential diagnosis for livestock health issues. This tool:
  - Starts a diagnostic session with initial symptoms
  - Asks follow-up questions to narrow down the diagnosis
  - Continues until a complete diagnosis is reached
  - Provides confidence scores and treatment recommendations`,

    parameters: z.object({
      query: z
        .string()
        .describe("The symptom description or answer to a diagnostic question"),
      sessionId: z
        .string()
        .optional()
        .describe("Session ID for continuing an existing diagnostic session"),
    }),

    execute: async ({ query, sessionId }) => {
      try {
        // Clean up old sessions periodically
        if (Math.random() < 0.1) cleanupSessions();

        // Prepare API payload
        const apiPayload: any = {
          query: query.trim(),
        };

        // Add session ID if continuing existing session
        if (sessionId && activeSessions.has(sessionId)) {
          apiPayload.session_id = sessionId;
        }

        console.log("Making diagnostic API call:", {
          query: apiPayload.query,
          hasSessionId: !!apiPayload.session_id,
        });

        // Make API call to veterinary diagnostic service
        const response = await fetch(
          `${options.baseUrl}/v2/prediction/diagnosis`,
          {
            method: "POST",
            headers: options.headers,
            body: JSON.stringify(apiPayload),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            `API request failed: ${response.status} - ${errorText}`
          );
          throw new Error(`Diagnostic API request failed: ${response.status}`);
        }

        if (response.status === 204) {
          return "No diagnosis Recomendation Found, Please proceed with adding it manually";
        }

        const data = await response.json();
        console.log;
        console.log("API response received:", {
          sessionId: data.session_id,
          isCompleted: data.data?.is_completed,
          hasQuestion: !!data.data?.question,
          diagnosesCount: data.data?.top_diagnoses?.length || 0,
        });

        // Update session information
        if (data.session_id) {
          activeSessions.set(data.session_id, {
            sessionId: data.session_id,
            isCompleted: data.data.is_completed,
            currentQuestion: data.data.question,
            questionNumber: data.data.question_number,
            maxQuestions: data.data.max_questions,
            lastUpdated: new Date(),
          });
        }

        // Process and validate diagnoses
        const processedDiagnoses = data.data?.diagnosis?.name;
        const processedDiagnosesId = data.data?.diagnosis?.diagnosis_id;
        const processedDiagnosisConfidenceScore =
          data.data?.diagnosis?.confidence_score;
        const otherRecommendedDiagnosis =
          data.data.top_diagnoses?.map((diagnosis: DiagnosisResult) => ({
            // ...diagnosis,
            name: diagnosis.name,
            confidence_percentage: Math.round(diagnosis.confidence_score * 100),
          })) || [];

        // Format diagnosis summary for LLM when completed
        //   let diagnosisSummary = ""
        //   if (data.data.is_completed && processedDiagnoses) {
        //     diagnosisSummary = formatDiagnosisForLLM(processedDiagnoses, assessUrgency(processedDiagnoses))
        //   }

        const allDiagnoses = [...otherRecommendedDiagnosis].slice(0, 3);

        if (!data.data.is_completed) {
          return {
            success: true,
            session_id: data.session_id,
            question: data.data.question,
            question_number: data.data.question_number,
            session_info: activeSessions.get(data.session_id),
          };
        }

        return {
          success: true,
          session_id: data.session_id,
          is_completed: data.data.is_completed,
          question: data.data.question,
          question_number: data.data.question_number,
          diagnosis: allDiagnoses,
          diagnosis_id: processedDiagnosesId,
          // otherRecommendedDiagnosis : otherRecommendedDiagnosis,
          diagnosisConfidenceScore: processedDiagnosisConfidenceScore,
          session_info: activeSessions.get(data.session_id),
        };
      } catch (error) {
        console.error("Differential diagnosis error:", error);

        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to perform diagnostic analysis",
          suggestion:
            "Please try rephrasing your symptoms or check your internet connection. If the problem persists, consult a veterinarian directly.",
        };
      }
    },
  });

// Export session management functions
export const sessionManager = {
  getSession: (sessionId: string) => activeSessions.get(sessionId),
  getAllSessions: () => Array.from(activeSessions.values()),
  clearSession: (sessionId: string) => activeSessions.delete(sessionId),
  clearAllSessions: () => activeSessions.clear(),
};

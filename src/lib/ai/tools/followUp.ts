import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";

import { RequestOptions } from "@/types/RequestOption";
import { followUpDetailsSchema } from "./schema/followup-details";

export const followUp = (options: RequestOptions) =>
  tool({
    description: "tool to get follow up date if follow up is necessary",
    parameters: followUpDetailsSchema,
    execute: async ({
      followUpRequired,
      followUpDate,
      followUpDateGMT,
    }) => {
      if (!followUpRequired) {
        return {
          found: false,
          message: "Follow up is not required",
          matches: [],
        };
      }
      if (!followUpDate) {
        return {
          found: false,
          message: "Follow up date is required",
          matches: [],
        };
      }
      return {
        found: true,
        message: "Follow up date is recorded",
        followUpRequired: followUpRequired,
        followUpDate: followUpDate,
        followUpDateGMT: followUpDateGMT,
      };
    },
  });

import { tool } from "ai";
import { z } from "zod";

export const medicationSchema = z.object({
  name: z.string(),
  reference: z.string(),
  route: z.string(),
  dosage: z.string(),
  frequency: z.string(),
});

const diagnosisSchema = z.object({
    name: z.string(),
    reference: z.string(),
  })

export const prescriptionSchema = z.object({
  patientDetails: z.object({
    customerName: z.string(),
    animalName: z.string(),
    animalType: z.string(),
    careCalendarId: z.string(),
    mobileNumber: z.string(),
    villageName: z.string(),
    animalId: z.string(),
    activityStatus: z.string(),
  }),
  caseDescription: z.object({
    symptoms: z.string(),
    temperature: z.string(),
    weight: z.string(),
  }),
  diagnosis: z.array(diagnosisSchema),
  medications: z.array(medicationSchema),
  followup: z.string().nullable().optional(),
  advisoryNotes: z.string().optional(),
});

export type Prescription = z.infer<typeof prescriptionSchema>;


export const prescriptionSummaryTool = () =>
  tool({
    description: "Generates a summary of the veterinary prescription including patient details, diagnosis, medication, followup and advisory notes.",
    parameters: prescriptionSchema,
    execute : async (prescriptionSummary)=>{
        return {
            message : "Summary generated",
            result : {
                ...prescriptionSummary
            }
        }
    }
  });

import { updateCaseDescription } from "@/lib/services/observation";
import { RequestOptions } from "@/types/RequestOption";
import { tool } from "ai";
import { z } from "zod";
import { caseDescriptionSchema } from "./schema/case-description";

export const recordCaseDescription = (options: RequestOptions) =>
  tool({
    description: "Tool to ask for case description",
    parameters: caseDescriptionSchema,
    execute: async ({ careCalenderId, caseDescription }) => {
      try {
        await updateCaseDescription(
          {
            careCalenderId,
            caseDescription,
          },
          options
        );

        return { message: "Case description updated" };
      } catch (error) {
        console.error("Error updating case description:", error);
        return { error: "Failed to update case description" };
      }
    },
  });

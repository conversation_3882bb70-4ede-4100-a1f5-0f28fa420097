import {
  getAdditionalTaskDetails,
  getTaskBySrNumber,
} from "@/lib/services/task";
import { RequestOptions } from "@/types/RequestOption";
import { tool } from "ai";
import { z } from "zod";
import { careCalenderIdSchema } from "./zod-schema/zod-schema";

export const getComplaintDetailsById = (options: RequestOptions) =>
  tool({
    description: "tool to get complaint details by care calendar id",
    parameters: z.object({
      careCalendarId: careCalenderIdSchema,
    }),
    execute: async ({ careCalendarId }) => {
      
      const additionDetails = await getAdditionalTaskDetails(
        careCalendarId,
        options
      );
      delete additionDetails.diagnosis;
      delete additionDetails.observation_category_configuration;
      delete additionDetails.observation;
      delete additionDetails.prescriptionPDF;
      delete additionDetails.payment_detail_break_up;
      delete additionDetails.medicines;
      delete additionDetails.prescription;

      const sanitizedData = {...additionDetails };

      console.log("sanitizedData", sanitizedData);

      const data = {
        customerId: sanitizedData?.customer_id,
        customerName: sanitizedData?.customer_name_l10n,
        mobileNumber: sanitizedData?.farmer_contact,
        complaintNumber: sanitizedData?.ticket_1,
        villageName: sanitizedData?.village_name_l10n,
        careCalendarId: sanitizedData?.care_calendar_id,
        animalVisualId: sanitizedData?.animal_visual_id,
        animalName: sanitizedData?.animal_name,
        animalType: sanitizedData?.animal_type_json,
        // activityName: sanitizedData?.activity_name_l10n,
        activityStatus: sanitizedData?.calendar_activity_status_name_json,
        vetName: sanitizedData?.vet_name,
        animalId : sanitizedData?.entity_uuid,
      };
      console.log("data", data);
      return data;
    },
  });

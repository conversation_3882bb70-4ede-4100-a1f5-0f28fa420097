import { tool } from "ai";
import Fuse, { IFuseOptions } from "fuse.js";
import { diagnosisList } from "@/lib/services/diagnosis";

type Diagnosis = {
  id: string;
  name: string;
};
import { RequestOptions } from "@/types/RequestOption";
import { checkDiagnosisDetailsSchema } from "./schema/diagnosis-details";

// Initialize Fuse with options for better fuzzy searching
const fuseOptions: IFuseOptions<Diagnosis> = {
  keys: ["name", "id"],
  includeScore: true,
  includeMatches: true,
  shouldSort: true,
  threshold: 0.2, // Very strict
  minMatchCharLength: 3,
  location: 0,
  distance: 50,
  ignoreLocation: false,
  isCaseSensitive: false,
};

// const getCachedDiagnosisList = cache(async (careCalendarId: string, options: RequestOptions): Promise<Diagnosis[]> => {
//   return await diagnosisList(careCalendarId, options);
// });
const diagnosisCache = new Map<string, Diagnosis[]>();

const getCachedDiagnosisList = async (
  careCalendarId: string,
  options: RequestOptions
): Promise<Diagnosis[]> => {
  if (diagnosisCache.has(careCalendarId)) {
    return diagnosisCache.get(careCalendarId)!;
  }
  const result = await diagnosisList(careCalendarId, options);
  diagnosisCache.set(careCalendarId, result);
  return result;
};

export const checkDiagnosis = (options: RequestOptions) =>
  tool({
    description: "Tool to check if diagnosis is available in the list",
    parameters: checkDiagnosisDetailsSchema,
    execute: async ({ diagnosisName, careCalendarId }) => {
      // ✅ Validate inputs early
      if (!diagnosisName?.trim() || !careCalendarId?.trim()) {
        return {
          found: false,
          message:
            "Please provide a diagnosis and care calendar ID to search from the list.",
          matches: [],
        };
      }

      // ✅ Get diagnosis list
      const diagnosisListArray = await getCachedDiagnosisList(
        careCalendarId,
        options
      );

      // ✅ Use Fuse.js to fuzzy search
      const fuse = new Fuse(diagnosisListArray, fuseOptions);

      const searchResults = fuse.search(diagnosisName);

      const matches = searchResults.map((result) => ({
        id: result.item.id,
        name: result.item.name,
        score: result.score,
      }));

      const hasMatches = matches.length > 0;
      const isExactMatch =
        hasMatches && matches[0].score !== undefined && matches[0].score < 0.1;

      // ✅ Suppress message when multiple matches are shown in UI
      let message = "";
      if (!hasMatches) {
        message = "No matching diagnosis found, please try again.";
      } else if (matches.length === 1) {
        message = `Found 1 matching diagnosis.`;
      }
      // ⛔ No message for multiple matches to avoid duplication

      return {
        found: hasMatches,
        message,
        matches: matches.slice(0, 10),
        exactMatch: isExactMatch,
      };
    },
  });

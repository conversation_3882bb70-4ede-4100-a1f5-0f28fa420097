import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";
import { loadReferences, medicineList } from "@/lib/services/medicines";

const medicineRouteList = [
  { short_name: "oral", name: "Oral", id: 1009000009 },
  { short_name: "iv", name: "Intravenous", id: 1009000007 },
  { short_name: "im", name: "Intramuscular", id: 1009000003 },
  { short_name: "sc", name: "Subcutaneous", id: 1009000010 },
  { short_name: "epidural", name: "Epidural", id: 1009000001 },
  { short_name: "intrauterine", name: "Intrauterine", id: 1009000006 },
  { short_name: "intramammary", name: "Intramammary", id: 1009000004 },
  { short_name: "intraparetoneal", name: "Intraperitoneal", id: 1009000005 },
  { short_name: "local", name: "Local", id: 1009000008 },
];

type Medicine = {
  id: number | string;
  name: string;
  short_name: string;
};

type MedicineRoute = {
  id: string;
  name: string;
};
import { RequestOptions } from "@/types/RequestOption";
import { checkMedicineDetailsSchema } from "./schema/medicines-details";
import { cache } from "react";

// Initialize Fuse with options for better fuzzy searching
const fuseOptions = {
  keys: ["name", "id"],
  includeScore: true,
  includeMatches: true,
  shouldSort: true,
  threshold: 0.2, // Very strict
  minMatchCharLength: 3,
  location: 0,
  distance: 50,
  ignoreLocation: false,
  isCaseSensitive: false,
};

// First, add a new Fuse option for routes near the existing fuseOptions
const routeFuseOptions = {
  keys: ["name", "id", "short_name"],
  includeScore: true,
  threshold: 0.3,
  minMatchCharLength: 2,
};

const medicineCache = new Map<string, Medicine[]>();

const getCachedMedicineList = async (
  options: RequestOptions
): Promise<Medicine[]> => {
  const cacheKey = options.baseUrl; // or any other stable unique identifier

  if (medicineCache.has(cacheKey)) {
    return medicineCache.get(cacheKey)!;
  }

  const result = await medicineList(options);
  medicineCache.set(cacheKey, result);
  return result;
};

export const checkMedicine = (options: RequestOptions) =>
  tool({
    description: "Tool to check if medicine is available in the list",
    parameters: checkMedicineDetailsSchema,
    execute: async ({ medicines }) => {
      console.log("medicines: ", medicines);

      if (!medicines || medicines.length === 0) {
        return {
          found: false,
          message: "At least 1 medicine is needed to check with the list",
          matches: [],
        };
      }

      const localMeds = await getCachedMedicineList(options);
      const fuse = new Fuse(localMeds, fuseOptions);

      const results = medicines.map((med) => {
        const searchResults = fuse.search(med.name);
        console.log("searchResults", searchResults, "this");

        const matches = searchResults.map((result) => ({
          id: result.item.id,
          name: result.item.name,
          score: result.score,
        }));

        return {
          searchedFor: med.name,
          matches: matches.slice(0, 4),
          found: matches.length > 0,
          exactMatch:
            matches.length > 0 &&
            matches[0].score !== undefined &&
            matches[0].score < 0.1,
        };
      });

      console.log("RESULT", results);

      return {
        found: results.some((result) => result.found),
        message: results.some((result) => result.found)
          ? "Found matches for some or all of the requested medicines"
          : "No matching medicines found in our database",
        results,
      };
    },
  });

export const checkMedicineRoute = (options: RequestOptions) =>
  tool({
    description:
      "Tool to check if medicine administration route is available in the list",
    parameters: z.object({
      administrationRouteName: z
        .string()
        .describe("route name is required to check with the list"),
    }),
    execute: async ({ administrationRouteName }) => {
      if (!administrationRouteName) {
        return {
          found: false,
          message: "Need to know Route",
          matches: [],
        };
      }

      // const routeListArray: MedicineRoute[] = await loadReferences(
      //   options,
      //   10090000
      // );
      const routeFuse = new Fuse(medicineRouteList, routeFuseOptions);

      // Search for route matches
      const searchResults = routeFuse.search(administrationRouteName);

      console.log("searchResults", searchResults);

      // Format results for the route
      const matches = searchResults.map((result) => ({
        id: result?.item?.id,
        name: result?.item?.name,
        score: result.score,
        requestedAdministrationRouteName: administrationRouteName,
      }));

      const result = {
        searchedFor: administrationRouteName,
        matches: matches.slice(0, 4),
        found: matches.length > 0,
        exactMatch:
          matches.length > 0 &&
          matches[0]?.score !== undefined &&
          matches[0].score < 0.1,
      };

      console.log("RESULT", result);

      return {
        found: result.found,
        message: result.found
          ? `Found matches for the requested route`
          : "No matching route found in our database",
        results: [result], // Wrapped in array to match checkMedicine format
      };
    },
  });
// export const updateMedicinesTool = tool({
//   description:
//     "tool to update medicine. this should be called once medicines is talken from user and is confirmed by the user",
//   parameters: z.object({
//     activity_id: z
//       .string()
//       .describe(
//         "activity id is care calendar id. get medicines id array from the user input "
//       )
//       .min(1, "activity_id is required"),
//     medicine: z
//       .array(
//         z.object({
//           id: z.string().optional().describe("medicine id is required"),
//           dosage: z.number().optional().describe("dosage of the medicine"),
//           prescribed_info: z
//             .object({
//               unit: z.string().describe("unit of the medicine dosage"),
//               route: z.number().describe("route of the medicine"),
//               frequency: z.string().describe("frequency of the medicine"),
//               status: z.string().describe("status of the medicine"),
//             })
//             .optional(),
//         })
//       )
//       .min(1, "At least one medicine is required")
//       .describe("Array of medicine objects to update"),
//     follow_up_required: z
//       .boolean()
//       .describe("Indicates if a follow-up is required"),
//     vet_cosultant_type: z
//       .number()
//       .describe("Type of vet consultant for the medicine"),
//   }),
//   execute: async (updateDiagnosisParams: updateMedicinesParams) => {
//     await updateMedicines(updateDiagnosisParams);
//     return {
//       message: "updated",
//     };
//   },
// });

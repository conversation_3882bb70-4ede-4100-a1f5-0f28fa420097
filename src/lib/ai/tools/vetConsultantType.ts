import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";
import { medicineList, loadReferences } from "@/lib/services/medicines";

type Medicine = {
  id: string;
  name: string;
};

type MedicineRoute = {
  id: string;
  name: string;
};
import { RequestOptions } from "@/types/RequestOption";

// Initialize Fuse with options for better fuzzy searching
const fuseOptions = {
  keys: ["name", "id"],
  includeScore: true,
  threshold: 0.4, // Lower threshold means more strict matching
  minMatchCharLength: 3,
};

type VetConsultant = {
  id: string;
  name: string;
};

export const getVetConsultantType = (options: RequestOptions) =>
  tool({
    description: "tool to get vet consultant type",
    parameters: z.object({
      vetConsultantType: z
        .string()
        .describe(
          "Type of vet where he was during consultation. it can be either Online(ID:1000750001) or Physical(ID:1000750002)"
        ),
    }),
    execute: async ({ vetConsultantType }) => {
      if (!vetConsultantType) {
        return {
          found: false,
          message: "Need to know Vet Consultant Type",
          matches: [],
        };
      }
      const vetConsultantList: VetConsultant[] = await loadReferences(
        options,
        10007500
      );
      const fuse = new Fuse(vetConsultantList, fuseOptions);

      // Search for medicine matches
      const searchResults = fuse.search(vetConsultantType);

      // Check if route is not found

      // Format results for this medicine
      const matches = searchResults.map((result) => ({
        id: result?.item?.id,
        name: result?.item?.name,
        score: result.score,
      }));

      return {
        searchedFor: vetConsultantType,
        matches: matches.slice(0, 4),
        found: matches.length > 0,
        exactMatch:
          matches.length > 0 &&
          matches[0]?.score !== undefined &&
          matches[0].score < 0.1,
      };
    },
  });

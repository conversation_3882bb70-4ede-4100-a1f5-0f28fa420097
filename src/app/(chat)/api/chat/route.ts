import {
  createDataStream,
  smoothStream,
  streamText,
  NoSuchToolError,
  generateObject,
} from "ai";
import { systemPrompt } from "@/lib/ai/prompts";
import { systemPrompt as systemPromptSeniorVet } from "@/lib/ai/prompts.seniorvet";
import { generateUUID } from "@/lib/utils";
import { isProductionEnvironment } from "@/lib/constants";
import { myProvider } from "@/lib/ai/providers";
import { postRequestBodySchema, type PostRequestBody } from "./schema";
import { generatePrescription } from "@/lib/ai/tools/generatePrescription";
import { getComplaintDetailsById } from "@/lib/ai/tools/getComplaintDetailsById";
import { checkDiagnosis } from "@/lib/ai/tools/checkDiagnosis";
import {
  checkMedicine,
  checkMedicineRoute,
} from "@/lib/ai/tools/checkMedicine";
import { RequestOptions } from "@/types/RequestOption";
import { recordCaseDescription } from "@/lib/ai/tools/recordCaseDescription";
import { followUp } from "@/lib/ai/tools/followUp";
import {
  medicineRecommendation,
  medicineRecommendationV2,
} from "@/lib/ai/tools/medicineRecommendation";
import { administrationCheckTool } from "@/lib/ai/tools/administrationCheckTool";
import { differentialDiagnosisTool } from "@/lib/ai/tools/differential-diagnosis-tool";
import { prescriptionSummaryTool } from "@/lib/ai/tools/prescription-summary-tool";

export const maxDuration = 60;

//MISTRAL_API_KEY

export async function POST(request: Request) {
  let requestBody: PostRequestBody;
  let requestHeaders: Headers;
  let authToken: string;
  let baseURL: string;
  let role: string;
  try {
    requestHeaders = request.headers;
    authToken =
      requestHeaders.get("authtoken")?.trim() || process.env.Token || "";
    baseURL =
      requestHeaders.get("baseurl")?.trim() ||
      process.env.BASE_URL?.trim() ||
      "";
    role = requestHeaders.get("role")?.trim() || "";
    console.log("Role here", role, baseURL);
  } catch (_) {
    return new Response("Invalid request headers", { status: 400 });
  }

  const options: RequestOptions = {
    baseUrl: baseURL,
    headers: {
      "Content-Type": "application/json",
      Token: authToken,
    },
    role: role,
  };

  try {
    requestBody = await request.json();
    // requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new Response("Invalid request body", { status: 400 });
  }

  try {
    const { id, message, selectedChatModel, messages } = requestBody;

    console.log("Role there", role);

    // console.log("System Prompt", systemPrompt({selectedChatModel}))

    // Create a simple stream without database operations
    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel(selectedChatModel || "chat-model"),
          system:
            role === "**********"
              ? systemPromptSeniorVet({ selectedChatModel })
              : systemPrompt({ selectedChatModel }),
          messages,
          maxSteps: 12,
          maxRetries: 3,
          experimental_activeTools:
            selectedChatModel === "chat-model-reasoning"
              ? []
              : ([
                  "getComplaintDetailsById",
                  "checkDiagnosis",
                  role === "**********" && "medicineRecommendation",
                  "checkMedicine",
                  "generatePrescription",
                  "recordCaseDescription",
                  "checkMedicineRoute",
                  "followUp",
                  "administrationCheckTool",
                  role === "**********" && "differentialDiagnosisTool",
                  "prescriptionSummaryTool",
                ].filter(Boolean) as (
                  | "getComplaintDetailsById"
                  | "checkDiagnosis"
                  | "medicineRecommendation"
                  | "checkMedicine"
                  | "generatePrescription"
                  | "recordCaseDescription"
                  | "checkMedicineRoute"
                  | "followUp"
                  | "administrationCheckTool"
                  | "differentialDiagnosisTool"
                  | "prescriptionSummaryTool"
                )[]),
          experimental_transform: smoothStream({ chunking: "word" }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getComplaintDetailsById: getComplaintDetailsById(options),
            recordCaseDescription: recordCaseDescription(options),
            checkDiagnosis: checkDiagnosis(options),
            ...(role === "**********"
              ? { medicineRecommendation: medicineRecommendationV2(options) }
              : {}),
            checkMedicine: checkMedicine(options),
            checkMedicineRoute: checkMedicineRoute(options),
            followUp: followUp(options),
            generatePrescription: generatePrescription(options),
            administrationCheckTool: administrationCheckTool(),
            ...(role === "**********"
              ? {
                  differentialDiagnosisTool: differentialDiagnosisTool(options),
                }
              : {}),
            prescriptionSummaryTool: prescriptionSummaryTool(),
          },

          onError: ({ error }) => {
            console.error("Error from the streamText: ", error);
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.log(error);
        return "Oops, an error occurred!";
      },
    });

    return new Response(stream);
  } catch (error) {
    console.error("Error in chat API:", error);
    return new Response("An error occurred while processing your request!", {
      status: 500,
    });
  }
}

export async function GET(request: Request) {
  // Return an empty response for GET requests
  // In a real app, this would retrieve chat history
  return new Response(null, { status: 204 });
}

export async function DELETE(request: Request) {
  // Return a success response for DELETE requests
  // In a real app, this would delete a chat
  return new Response(null, { status: 200 });
}

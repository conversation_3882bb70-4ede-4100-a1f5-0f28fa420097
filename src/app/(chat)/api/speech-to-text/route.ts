import { groq } from "@ai-sdk/groq";

export async function POST(req: Request) {
  // No authentication check needed anymore

  try {
    const formData = await req.formData();
    const audioFile = formData.get("file") as File;

    if (!audioFile) {
      return Response.json(
        { error: "Audio file is required" },
        { status: 400 }
      );
    }

    // Check file size (10MB limit)
    if (audioFile.size > 10 * 1024 * 1024) {
      return Response.json(
        { error: "Audio file size exceeds 10MB limit" },
        { status: 400 }
      );
    }

    // Convert File to ArrayBuffer
    const arrayBuffer = await audioFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Transcribe the audio using Whisper via Groq API
    const transcriptionResponse = await fetch(
      "https://api.groq.com/openai/v1/audio/transcriptions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
        },
        body: (() => {
          const formData = new FormData();
          formData.append("file", new Blob([buffer]), "audio.webm");
          formData.append("model", "whisper-large-v3");
          formData.append("language", "en");
          return formData;
        })(),
      }
    );

    if (!transcriptionResponse.ok) {
      const errorData = await transcriptionResponse.json();
      return Response.json(
        {
          error: `Transcription failed: ${
            errorData.error?.message || "Unknown error"
          }`,
        },
        { status: 500 }
      );
    }

    const transcriptionData = await transcriptionResponse.json();
    const text = transcriptionData.text;

    return Response.json({
      success: true,
      text: text,
    });
  } catch (error) {
    console.error("Transcription error:", error);
    return Response.json(
      {
        error:
          "Failed to transcribe audio: " +
          (error instanceof Error ? error.message : "Unknown error"),
      },
      { status: 500 }
    );
  }
}

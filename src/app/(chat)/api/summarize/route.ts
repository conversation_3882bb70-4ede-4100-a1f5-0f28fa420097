import { NextResponse } from "next/server";
import { summarizeResponseFromLLM } from "../../actions";

export async function POST(request: Request) {
  try {
    // Parse the request body
    const { prompt } = await request.json();

    // Validate input
    if (!prompt) {
      return NextResponse.json(
        { error: "prompt is required" },
        { status: 400 }
      );
    }
    const response = await summarizeResponseFromLLM({ message: prompt });

    // Return the prompt as is
    return NextResponse.json({ response }, { status: 200 });
  } catch (error) {
    console.error("Error in summarize API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

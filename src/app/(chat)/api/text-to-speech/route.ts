export async function POST(req: Request) {
  try {
    const { text } = await req.json();

    if (!text) {
      return Response.json({ error: "Text is required" }, { status: 400 });
    }

    if (!process.env.HUGGINGFACE_API_KEY) {
      return Response.json(
        { error: "Hugging Face API key not configured" },
        { status: 500 }
      );
    }

    // Call Hugging Face TTS API
    const response = await fetch(
      "https://router.huggingface.co/fal-ai/fal-ai/dia-tts",
      {
        headers: {
          Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({ text }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`
      );
    }

    const result = await response.json();

    // The API should return audio data or a URL to the audio
    // Return the result as-is for the client to handle
    return Response.json({
      success: true,
      audio: result,
    });
  } catch (error) {
    console.error("TTS error:", error);
    return Response.json(
      {
        error:
          "Failed to process text-to-speech request: " +
          (error instanceof Error ? error.message : "Unknown error"),
      },
      { status: 500 }
    );
  }
}

import { NextRequest } from "next/server";
import { getChatsByUserId } from "@/lib/db/queries";
import { cookies } from "next/headers";
import { generateUUID } from "@/lib/utils";

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;

  const limit = parseInt(searchParams.get("limit") || "10");
  const startingAfter = searchParams.get("starting_after");
  const endingBefore = searchParams.get("ending_before");

  if (startingAfter && endingBefore) {
    return Response.json(
      "Only one of starting_after or ending_before can be provided!",
      { status: 400 }
    );
  }

  // Get user ID from cookie or generate a new one
  const cookieStore = await cookies();
  const userId = cookieStore.get("user-id")?.value || generateUUID();

  try {
    const chats = await getChatsByUserId({
      id: userId,
      limit,
      startingAfter,
      endingBefore,
    });

    return Response.json(chats);
  } catch (_) {
    return Response.json("Failed to fetch chats!", { status: 500 });
  }
}

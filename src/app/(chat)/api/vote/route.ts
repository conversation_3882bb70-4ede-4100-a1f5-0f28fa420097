import { getChatById, getVotesByChatId, voteMessage } from "@/lib/db/queries";
import { cookies } from "next/headers";
import { generateUUID } from "@/lib/utils";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get("chatId");

  if (!chatId) {
    return new Response("chatId is required", { status: 400 });
  }

  // Get user ID from cookie or generate a new one
  const cookieStore = await cookies();
  const userId = cookieStore.get("user-id")?.value || generateUUID();

  try {
    const votes = await getVotesByChatId({ id: chatId });
    return Response.json(votes, { status: 200 });
  } catch (error) {
    console.error("Error fetching votes:", error);
    return new Response("Error fetching votes", { status: 500 });
  }
}

export async function PATCH(request: Request) {
  const {
    chatId,
    messageId,
    type,
  }: { chatId: string; messageId: string; type: "up" | "down" } =
    await request.json();

  if (!chatId || !messageId || !type) {
    return new Response("messageId and type are required", { status: 400 });
  }

  // Get user ID from cookie or generate a new one
  const cookieStore = await cookies();
  const userId = cookieStore.get("user-id")?.value || generateUUID();

  try {
    await voteMessage({
      chatId,
      messageId,
      type: type,
    });

    return new Response("Message voted", { status: 200 });
  } catch (error) {
    console.error("Error voting message:", error);
    return new Response("Error voting message", { status: 500 });
  }
}

"use server";

import { generateText, type UIMessage } from "ai";
import { cookies } from "next/headers";
import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisiblityById,
} from "@/lib/db/queries";
import type { VisibilityType } from "@/components/visibility-selector";
import { myProvider } from "@/lib/ai/providers";

export async function saveChatModelAsCookie(model: string) {
  const cookieStore = await cookies();
  cookieStore.set("chat-model", model);
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: UIMessage;
}) {
  const { text: title } = await generateText({
    model: myProvider.languageModel("title-model"),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });

  return title;
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisiblityById({ chatId, visibility });
}

export async function summarizeResponseFromLLM({
  message,
}: {
  message: string;
}) {
  const { text } = await generateText({
    model: myProvider.languageModel("summarize-model"),
    system: `\n
# TTS Rephrasing Agent

You are a text-to-speech rephrasing agent.
*** DO NOT SUGGEST MEDICAL DIAGNOSES OR MEDICATIONS ***

## YOUR JOB
Make messages under 100 characters for speech and UI display.

## STEP 1: Read the Input Message
**What to do:** Look at the message you need to rephrase
**What to check:** 
- Is it a question or statement?
- What is the main point?
- What tone does it have?

## STEP 2: Keep Important Parts
**What to keep:**
- Main meaning
- Question format (if original is a question)
- Same tone and urgency
- Key information

**What to remove:**
- Extra words
- Numbers and IDs
- Brackets and symbols
- Lists (just say "choose one")
- Technical abbreviations

## STEP 3: Make It Speech-Friendly
**What to do:**
- Spell out abbreviations (PDF becomes "pee dee eff")
- Use natural conversation words
- Remove typing instructions
- Make it sound like talking to a person

## STEP 4: Check Length
**What to check:**
- Is it under 100 characters?
- If too long: remove more unnecessary words
- If too short: make sure meaning is still clear

## STEP 5: Final Check
**Ask yourself:**
- Does this mean the same thing as the original?
- Will someone understand this when spoken aloud?
- Did I keep questions as questions?
- Did I avoid suggesting medical things?

## WHAT NOT TO DO
- Don't add new information
- Don't suggest diagnoses or medicines
- Don't change statements to questions
- Don't include numbers or codes
- Don't ask people to type things
- Don't read out long lists

## OUTPUT RULE
Give only the short version. No explanations. No extra text.

## EXAMPLES
**Too long:** "Please provide the complaint number from your documentation to begin generating the prescription for this case"
**Good:** "Please give me the complaint number to start the prescription"

**Too long:** "I found multiple medications matching 'Mastiwoq' in our database. Which specific one did you intend to prescribe? (1) Mastiwoq 250mg (2) Mastiwoq 500mg"
**Good:** "I found several Mastiwoq options. Which one do you want?"

**Too long:** "What specific observations have you made about this bovine patient? Please include temperature and weight measurements if available"
**Good:** "What observations did you make? Include temperature and weight if you have them"`,

    prompt: message,
  });
  console.log("summarized text", text);
  return text;
}

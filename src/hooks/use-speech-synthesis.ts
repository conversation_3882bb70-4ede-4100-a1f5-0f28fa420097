"use client";

import { useState, useEffect, useRef, useCallback } from "react";

type SpeechState = "idle" | "speaking" | "paused" | "ended" | "processing";

interface UseSpeechSynthesisProps {
  onEnd?: () => void;
  onError?: (error: SpeechSynthesisErrorEvent | Error) => void;
  onBoundary?: (event: SpeechSynthesisEvent) => void;
  onApiStart?: () => void;
  onApiEnd?: () => void;
  filterLLMOutput?: boolean;
  skipTechnicalTerms?: boolean;
  apiEndpoint?: string;
  useWebTTS?: boolean; // New prop to choose between Web TTS and API TTS
}

interface UseSpeechSynthesisReturn {
  speak: (text: string) => Promise<void>;
  speakRaw: (text: string) => Promise<void>;
  speakWithAPI: (prompt: string) => Promise<void>;
  cancel: () => void;
  pause: () => void;
  resume: () => void;
  state: SpeechState;
  isSupported: boolean;
  setVolume: (volume: number) => void;
  volume: number;
  isSpeaking: boolean;
  isProcessing: boolean;
  toggleFiltering: () => void;
  isFilteringEnabled: boolean;
  lastApiResponse: string | null;
  apiError: string | null;
  ttsError: string | null;
}

export function useSpeechSynthesis({
  onEnd,
  onError,
  onBoundary,
  onApiStart,
  onApiEnd,
  filterLLMOutput = true,
  skipTechnicalTerms = true,
  apiEndpoint = "/api/summarize",
  useWebTTS = true, // Default to Web TTS
}: UseSpeechSynthesisProps = {}): UseSpeechSynthesisReturn {
  const [state, setState] = useState<SpeechState>("idle");
  const [volume, setVolume] = useState<number>(1);
  const [isFilteringEnabled, setIsFilteringEnabled] =
    useState<boolean>(filterLLMOutput);
  const [lastApiResponse, setLastApiResponse] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [ttsError, setTtsError] = useState<string | null>(null);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const apiCallInProgressRef = useRef<boolean>(false);
  const lastTtsErrorRef = useRef<number>(0);

  const isSupported = typeof window !== "undefined";
  const isSpeaking = state === "speaking" || state === "paused";
  const isProcessing = state === "processing";

  // Text filtering function
  const filterText = useCallback(
    (text: string): string => {
      if (!isFilteringEnabled) return text;

      let filtered = text;

      if (skipTechnicalTerms) {
        filtered = filtered
          .replace(/```[\s\S]*?```/g, " [code block] ") // Code blocks
          .replace(/`[^`]+`/g, " [code] ") // Inline code
          .replace(/https?:\/\/[^\s]+/g, " [link] ") // URLs
          .replace(/\b[A-Z_]{3,}\b/g, " [constant] ") // ALL_CAPS constants
          .replace(/\b\w+\.\w+\(/g, " [function call] ") // Function calls
          .replace(/[{}[\]()]/g, " ") // Remove brackets and parentheses
          .replace(/\s+/g, " ") // Normalize whitespace
          .trim();
      }

      return filtered;
    },
    [isFilteringEnabled, skipTechnicalTerms]
  );

  // API call function for summarizing text to make it TTS-friendly
  const callSummarizeAPI = useCallback(
    async (prompt: string): Promise<string> => {
      try {
        setApiError(null);
        console.log(
          "Calling summarize API for TTS-friendly text:",
          prompt.substring(0, 100) + "..."
        );

        const response = await fetch(apiEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ prompt }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.response) {
          throw new Error("Invalid response format from API");
        }

        setLastApiResponse(data.response);
        console.log(
          "Summarized text for TTS:",
          data.response.substring(0, 100) + "..."
        );
        return data.response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        setApiError(errorMessage);
        console.error("Summarize API call failed:", error);

        // Fallback to original text if summarize fails
        console.log("Using original text as fallback");
        setLastApiResponse(prompt);
        return prompt;
      }
    },
    [apiEndpoint]
  );

  // Web TTS function using browser speech synthesis
  const speakTextWithWebTTS = useCallback(
    async (text: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("TTS not supported");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      if (!text?.trim()) {
        const error = new Error("Empty text provided");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      if (typeof window !== "undefined" && "speechSynthesis" in window) {
        try {
          console.log(
            "Using Web TTS for text:",
            text.substring(0, 100) + "..."
          );

          // Cancel any existing speech
          window.speechSynthesis.cancel();

          const utterance = new SpeechSynthesisUtterance(text);
          utterance.volume = Math.max(0, Math.min(1, volume));
          utterance.rate = 1.0;
          utterance.pitch = 1.0;

          utterance.onstart = () => {
            console.log("Web TTS started");
            setState("speaking");
          };

          utterance.onend = () => {
            console.log("Web TTS ended");
            setState("ended");
            if (onEnd) onEnd();
          };

          utterance.onerror = (event) => {
            console.error("Web TTS error:", event.error);
            setState("idle");
            if (onError) onError(event);
          };

          setState("processing");
          window.speechSynthesis.speak(utterance);
          return;
        } catch (error) {
          console.error("Web TTS failed:", error);
          setState("idle");
          if (onError)
            onError(
              error instanceof Error ? error : new Error("Web TTS failed")
            );
          throw error;
        }
      } else {
        const error = new Error("Web Speech API not supported");
        console.error(error.message);
        setState("idle");
        if (onError) onError(error);
        throw error;
      }
    },
    [isSupported, volume, onEnd, onError]
  );

  // API-based TTS function
  const speakTextWithAPI = useCallback(
    async (text: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("TTS not supported");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      if (!text?.trim()) {
        const error = new Error("Empty text provided");
        console.error(error.message);
        if (onError) onError(error);
        throw error;
      }

      // Check if we had a recent TTS error (cooldown period of 30 seconds)
      const now = Date.now();
      if (now - lastTtsErrorRef.current < 30000) {
        console.log("TTS cooldown active, using fallback speech synthesis");
        // Use fallback immediately if in cooldown
        await speakTextWithWebTTS(text);
        return;
      }

      try {
        setTtsError(null);
        setState("processing");

        // Call our TTS API
        const response = await fetch("/api/text-to-speech", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ text }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.error || `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.success || !data.audio) {
          throw new Error("Invalid response from TTS API");
        }

        // Handle audio playback
        // The API response should contain audio data or URL
        await playAudio(data.audio);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown TTS error";
        setTtsError(errorMessage);
        lastTtsErrorRef.current = Date.now(); // Set cooldown timestamp
        console.error("TTS error:", error);

        // Fallback to Web TTS if available
        console.log("Falling back to Web TTS");
        try {
          await speakTextWithWebTTS(text);
          return; // Don't throw error if fallback works
        } catch (fallbackError) {
          console.error("Fallback Web TTS failed:", fallbackError);
        }

        setState("idle");
        if (onError)
          onError(error instanceof Error ? error : new Error(errorMessage));
        throw error;
      }
    },
    [isSupported, volume, onEnd, onError]
  );

  // Audio playback function
  const playAudio = useCallback(
    async (audioData: any): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          // Cancel any existing audio
          if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current = null;
          }

          // Create new audio element
          const audio = new Audio();
          audioRef.current = audio;

          // Set volume
          audio.volume = Math.max(0, Math.min(1, volume));

          // Event handlers
          audio.onloadstart = () => {
            console.log("Audio loading started");
          };

          audio.oncanplay = () => {
            console.log("Audio can start playing");
            setState("speaking");
          };

          audio.onended = () => {
            console.log("Audio ended");
            setState("ended");
            if (onEnd) onEnd();
            resolve();
          };

          audio.onerror = (event) => {
            console.error("Audio error:", event);
            setState("idle");
            const error = new Error("Audio playback failed");
            if (onError) onError(error);
            reject(error);
          };

          // Handle different audio data formats
          if (typeof audioData === "string") {
            // If it's a URL or base64 string
            audio.src = audioData;
          } else if (audioData.url) {
            // If it's an object with URL
            audio.src = audioData.url;
          } else if (audioData.data) {
            // If it's base64 data
            audio.src = `data:audio/wav;base64,${audioData.data}`;
          } else {
            throw new Error("Unsupported audio data format");
          }

          // Start playing
          audio.play().catch((error) => {
            console.error("Failed to play audio:", error);
            setState("idle");
            if (onError) onError(error);
            reject(error);
          });
        } catch (error) {
          console.error("Error setting up audio:", error);
          setState("idle");
          if (onError)
            onError(
              error instanceof Error ? error : new Error("Audio setup failed")
            );
          reject(error);
        }
      });
    },
    [volume, onEnd, onError]
  );

  // Main speak function with filtering
  const speak = useCallback(
    async (text: string): Promise<void> => {
      const filteredText = filterText(text);

      if (!filteredText.trim()) {
        console.log("Text was filtered out completely, skipping speech");
        return;
      }

      if (useWebTTS) {
        console.log(
          "Speaking filtered text with Web TTS:",
          filteredText.substring(0, 100) + "..."
        );
        await speakTextWithWebTTS(filteredText);
      } else {
        console.log(
          "Speaking filtered text with TTS API:",
          filteredText.substring(0, 100) + "..."
        );
        await speakTextWithAPI(filteredText);
      }
    },
    [filterText, useWebTTS, speakTextWithWebTTS, speakTextWithAPI]
  );

  // Raw speak function without filtering
  const speakRaw = useCallback(
    async (text: string): Promise<void> => {
      if (useWebTTS) {
        console.log(
          "Speaking raw text with Web TTS:",
          text.substring(0, 100) + "..."
        );
        await speakTextWithWebTTS(text);
      } else {
        console.log(
          "Speaking raw text with TTS API:",
          text.substring(0, 100) + "..."
        );
        await speakTextWithAPI(text);
      }
    },
    [useWebTTS, speakTextWithWebTTS, speakTextWithAPI]
  );

  // Speak with API integration - summarizes text first to make it TTS-friendly
  const speakWithAPI = useCallback(
    async (prompt: string): Promise<void> => {
      if (!isSupported) {
        const error = new Error("TTS not supported");
        if (onError) onError(error);
        throw error;
      }

      if (!prompt?.trim()) {
        const error = new Error("Empty prompt provided");
        if (onError) onError(error);
        throw error;
      }

      // Prevent multiple concurrent API calls
      if (apiCallInProgressRef.current) {
        console.log("API call already in progress, skipping");
        return;
      }

      try {
        apiCallInProgressRef.current = true;

        // Cancel any existing audio
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current = null;
        }
        setState("processing");

        if (onApiStart) onApiStart();

        console.log(
          "Calling summarize API with prompt:",
          prompt.substring(0, 100) + "..."
        );

        const apiResponse = await callSummarizeAPI(prompt);

        if (onApiEnd) onApiEnd();
        console.log(
          "API response received:",
          apiResponse.substring(0, 100) + "..."
        );

        const filteredText = filterText(apiResponse);

        if (!filteredText.trim()) {
          console.log(
            "API response was filtered out completely, skipping speech"
          );
          setState("idle");
          return;
        }

        console.log("Starting TTS for summarized text");
        if (useWebTTS) {
          await speakTextWithWebTTS(filteredText);
        } else {
          await speakTextWithAPI(filteredText);
        }
      } catch (error) {
        console.error("Error in speakWithAPI:", error);
        setState("idle");
        if (onError)
          onError(error instanceof Error ? error : new Error("Unknown error"));
        // Don't re-throw the error to prevent loops
        return;
      } finally {
        apiCallInProgressRef.current = false;
      }
    },
    [
      isSupported,
      callSummarizeAPI,
      onApiStart,
      onApiEnd,
      filterText,
      useWebTTS,
      speakTextWithWebTTS,
      speakTextWithAPI,
      onError,
    ]
  );

  // Control functions
  const cancel = useCallback(() => {
    if (!isSupported) return;

    console.log("Canceling audio playback");

    // Cancel HTML audio (for API TTS)
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Cancel browser speech synthesis (for Web TTS)
    if (typeof window !== "undefined" && "speechSynthesis" in window) {
      window.speechSynthesis.cancel();
    }

    setState("idle");
  }, [isSupported]);

  const pause = useCallback(() => {
    if (!isSupported) return;

    if (useWebTTS) {
      // Web Speech API doesn't support pause, so we cancel instead
      console.log("Canceling Web TTS (pause not supported)");
      if (typeof window !== "undefined" && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
      setState("idle");
    } else {
      // API TTS supports pause
      if (audioRef.current) {
        console.log("Pausing audio playback");
        audioRef.current.pause();
        setState("paused");
      }
    }
  }, [isSupported, useWebTTS]);

  const resume = useCallback(() => {
    if (!isSupported) return;

    if (useWebTTS) {
      // Web Speech API doesn't support resume, so this is a no-op
      console.log("Resume not supported for Web TTS");
      return;
    } else {
      // API TTS supports resume
      if (audioRef.current) {
        console.log("Resuming audio playback");
        audioRef.current.play().catch((error) => {
          console.error("Failed to resume audio:", error);
          setState("idle");
        });
        setState("speaking");
      }
    }
  }, [isSupported, useWebTTS]);

  const toggleFiltering = useCallback(() => {
    setIsFilteringEnabled((prev) => !prev);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cancel HTML audio (for API TTS)
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Cancel browser speech synthesis (for Web TTS)
      if (typeof window !== "undefined" && "speechSynthesis" in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  return {
    speak,
    speakRaw,
    speakWithAPI,
    cancel,
    pause,
    resume,
    state,
    isSupported,
    setVolume,
    volume,
    isSpeaking,
    isProcessing,
    toggleFiltering,
    isFilteringEnabled,
    lastApiResponse,
    apiError,
    ttsError,
  };
}

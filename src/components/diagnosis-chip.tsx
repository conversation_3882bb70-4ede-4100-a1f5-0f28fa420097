import { Activity, AlertCircle } from "lucide-react";

interface Diagnosis {
  name: string;
  reference: string;
  score?: number;
}

interface DiagnosisListProps {
  diagnoses: Diagnosis[];
}

export default function DiagnosisListChip({ diagnoses }: DiagnosisListProps) {
  return (
    <div className="max-w-2xl">
      <div className="text-lg font-semibold text-gray-800 mb-3">
        Diagnosis List
      </div>
      <div className="space-y-2">
        {diagnoses.map((diagnosis, index) => (
          <div
            key={`${diagnosis.reference}-${index}`}
            className="flex items-start gap-3 py-2 hover:bg-gray-50 rounded-lg px-2 transition-colors duration-150"
          >
            <div className="w-6 h-6 bg-gray-100 rounded-full text-sm font-medium text-gray-600 flex items-center justify-center">
              {index + 1}
            </div>

            <div className="flex flex-col">
              <DiagnosisChip diagnosis={diagnosis.name} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

const getRandomLightColor = () => {
  const hue = Math.floor(Math.random() * 360); // Any hue
  return `hsl(${hue}, 100%, 90%)`; // Light pastel shade
};

function DiagnosisChip({ diagnosis }: { diagnosis: string }) {
  const bgColor = getRandomLightColor();

  return (
    <div
      className="inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900"
      style={{ backgroundColor: bgColor }}
    >
      {diagnosis}
    </div>
  );
}

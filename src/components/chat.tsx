"use client";

import type { Attachment, UIMessage } from "ai";
import { useChat } from "@ai-sdk/react";
import { useEffect, useState, useRef, useCallback } from "react";
import useSWR, { useSWRConfig } from "swr";
import { Cha<PERSON>Header } from "@/components/chat-header";
import type { Vote } from "@/lib/db/schema";
import { fetcher, generateUUID } from "@/lib/utils";
import { Artifact } from "./artifact";
import { MultimodalInput } from "./multimodal-input";
import { Messages } from "./messages";
import type { VisibilityType } from "./visibility-selector";
import { useArtifactSelector } from "@/hooks/use-artifact";
import { unstable_serialize } from "swr/infinite";
import { getChatHistoryPaginationKey } from "./sidebar-history";
import { toast } from "./toast";
import { useSearchParams } from "next/navigation";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import { useAutoResume } from "@/hooks/use-auto-resume";
import { ChatAudioControls } from "./chat-audio-controls";
import { useLocalStorage } from "usehooks-ts";
import { getAdditionalTaskDetails } from "@/lib/services/task";
import { RequestOptions } from "@/types/RequestOption";
import { extractBasedOnLanguage } from "@/lib/utils";
import { format } from "date-fns";

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: any; // Replace Session type with any
  autoResume: boolean;
}) {
  const { mutate } = useSWRConfig();

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
    data,
    addToolResult,
  } = useChat({
    id,
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    // experimental_prepareRequestBody: (body) => ({
    //   id,
    //   message: body.messages.at(-1),
    //   selectedChatModel: initialChatModel,
    //   selectedVisibilityType: visibilityType,
    // }),
    onFinish: () => {
      // mutate(unstable_serialize(getChatHistoryPaginationKey));
    },
    onError: (error) => {
      toast({
        type: "error",
        description: error.message,
      });
      stop();
    },
  });

  const searchParams = useSearchParams();

  const query = searchParams.get("query");

  const token = searchParams.get("token");

  const baseUrl = searchParams.get("baseUrl");

  const calendarId = searchParams.get("calendarId");
  console.log("calendarId", calendarId);

  const [taskDetails, setTaskDetails] = useState<any>(null);
  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (token) {
      console.log("Set Token", token);
      localStorage.setItem("token", token);
    }
  }, [token]);

  useEffect(() => {
    if (baseUrl) {
      localStorage.setItem("baseUrl", baseUrl);
    }
  }, [baseUrl]);

  useEffect(() => {
    if (calendarId) {
      localStorage.setItem("calendarId", calendarId);
    }
  }, [calendarId]);

  const getTaskDetails = async () => {
    try {
      const options: RequestOptions = {
        baseUrl: baseUrl || "",
        headers: {
          "Content-Type": "application/json",
          Token: token || "",
        },
      };
      const data = await getAdditionalTaskDetails(calendarId ?? "", options);
      setTaskDetails(data);
      return data;
    } catch (error) {
      return error;
    }
  };

  // useEffect(() => {
  //   if (taskDetails && !hasAppendedQuery) {
  //     append(
  //       {
  //         role: "user",
  //         content: taskDetails,
  //       },
  //       {
  //         headers: {
  //           authtoken: localStorage.getItem("token") || "",
  //           baseurl: localStorage.getItem("baseUrl") || "",
  //         },
  //       }
  //     );

  //     setHasAppendedQuery(true);
  //     window.history.replaceState({}, "", `/chat/${id}`);
  //   }
  // }, [query, append, hasAppendedQuery, id]);

  // Fetch task details when all required dependencies are available
  useEffect(() => {
    const fetchData = async () => {
      try {
        const options: RequestOptions = {
          baseUrl: baseUrl || "",
          headers: {
            "Content-Type": "application/json",
            Token: token || "",
          },
        };
        const data = await getAdditionalTaskDetails(calendarId ?? "", options);

        console.log("taskDetail", JSON.stringify(data));

        const customerName = extractBasedOnLanguage(
          data?.customer_name_l10n,
          "en"
        );
        const animalName = extractBasedOnLanguage(
          data?.activity_name_l10n,
          "en"
        );
        const animalType = extractBasedOnLanguage(
          JSON.parse(data?.animal_type_json || "{}"),
          "en"
        );
        const careCalendarId = data?.care_calendar_id ?? "";
        const mobileNumber = data?.farmer_contact ?? "";
        const villageName = extractBasedOnLanguage(
          data?.village_name_l10n,
          "en"
        );
        const animalId = data?.entity_uuid ?? "";
        const animalVisualId = data?.animal_visual_id ?? "";
        const activityStatus = extractBasedOnLanguage(
          data?.calendar_activity_status_name_json,
          "en"
        );
        const customerId = data?.customer_id ?? "";
        const activityDate = format(data?.activity_date, "PPPP") ?? new Date();
        const template = `
**Customer Name:** ${customerName}  
**Animal Name:** ${animalName}  
**Animal Type:** ${animalType}  
**Care Calendar ID:** ${careCalendarId}  
**Mobile Number:** ${mobileNumber}  
**Village Name:** ${villageName}  
**Animal ID:** ${animalId}  
**Activity Status:** ${activityStatus}  
**Farmer ID:** ${customerId}
**Activity Date:** ${activityDate}
`;

        setTaskDetails(template);
      } catch (error) {
        console.error("Failed to fetch task details:", error);
      }
    };

    if (calendarId && token && baseUrl && !taskDetails) {
      fetchData();
    }
  }, [calendarId, token, baseUrl, taskDetails]);

  // Append task details to conversation
  useEffect(() => {
    if (taskDetails && !hasAppendedQuery) {
      append(
        {
          role: "user",
          content: taskDetails,
        },
        {
          headers: {
            authtoken: localStorage.getItem("token") || "",
            baseurl: localStorage.getItem("baseUrl") || "",
          },
        }
      );

      setHasAppendedQuery(true);
      window.history.replaceState({}, "", `/chat/${id}`);
    }
  }, [taskDetails, append, hasAppendedQuery, id]);

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // Audio mode state
  const [audioMode, setAudioMode] = useState(false);
  const [recordingState, setRecordingState] = useState<
    "idle" | "recording" | "processing"
  >("idle");
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Get the last assistant message for TTS
  const lastAssistantMessage = messages
    .filter((msg) => msg.role === "assistant")
    .pop();

  // Extract text from the last assistant message for TTS
  const lastAssistantText = lastAssistantMessage
    ? typeof lastAssistantMessage.content === "string"
      ? lastAssistantMessage.content
      : Array.isArray(lastAssistantMessage.parts)
      ? lastAssistantMessage.parts
          .filter((part) => {
            if (typeof part === "string") return true;
            if (typeof part === "object" && part !== null && "text" in part)
              return true;
            return false;
          })
          .map((part) => {
            if (typeof part === "string") return part;
            if (typeof part === "object" && part !== null && "text" in part)
              return part.text;
            return "";
          })
          .join(" ")
      : ""
    : "";

  // Log the extracted text for debugging
  useEffect(() => {
    if (audioMode && lastAssistantText) {
      console.log("Last assistant text for TTS:", lastAssistantText);
    }
  }, [audioMode, lastAssistantText]);

  useEffect(() => {
    setTimeout(() => {
      console.log("Setting audioMode to true");
      setAudioMode(true);
    }, 1000);
  }, []);

  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 2048;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.fftSize);
      let silenceStart: number | null = null;
      const SILENCE_THRESHOLD = 0.01;
      const SILENCE_DURATION = 2000; // ms

      const detectSilence = () => {
        analyser.getByteTimeDomainData(dataArray);
        const rms = Math.sqrt(
          dataArray.reduce((sum, val) => {
            const norm = (val - 128) / 128;
            return sum + norm * norm;
          }, 0) / dataArray.length
        );

        const now = Date.now();

        if (rms < SILENCE_THRESHOLD) {
          if (!silenceStart) silenceStart = now;
          else if (now - silenceStart > SILENCE_DURATION) {
            mediaRecorder.stop();
            audioContext.close();
            return;
          }
        } else {
          silenceStart = null;
        }

        requestAnimationFrame(detectSilence);
      };

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setRecordingState("processing");

        try {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: "audio/webm",
          });

          const formData = new FormData();
          formData.append("file", audioBlob, "recording.webm");

          const response = await fetch("/api/speech-to-text", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to transcribe audio");
          }

          const data = await response.json();
          const transcribedText = data.text || data.transcript;

          if (transcribedText) {
            await append(
              {
                role: "user",
                content: transcribedText,
              },
              {
                headers: {
                  authtoken: localStorage.getItem("token") || "",
                  baseurl: localStorage.getItem("baseUrl") || "",
                },
              }
            );
          } else {
            toast({
              type: "error",
              description: "No speech detected. Please try again.",
            });
          }
        } catch (error) {
          console.error("Error processing audio:", error);
          toast({
            type: "error",
            description: "Failed to process audio. Please try again.",
          });
        } finally {
          setRecordingState("idle");
          if (mediaRecorderRef.current) {
            const tracks = (
              mediaRecorderRef.current.stream as MediaStream
            ).getTracks();
            tracks.forEach((track) => track.stop());
          }
        }
      };

      mediaRecorder.start();
      setRecordingState("recording");
      detectSilence(); // start monitoring for silence
    } catch (error) {
      console.error("Error starting recording:", error);
      toast({
        type: "error",
        description: "Could not access microphone. Please check permissions.",
      });
      setRecordingState("idle");
    }
  }, [append]);

  // Stop recording function
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState === "recording") {
      mediaRecorderRef.current.stop();
    }
  }, [recordingState]);

  useAutoResume({
    autoResume,
    initialMessages,
    experimental_resume,
    data,
    setMessages,
  });

  console.log("messages", messages);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        {/* <ChatHeader
          chatId={id}
          selectedModelId={initialChatModel}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          session={session}
          customerName={customerName}
        /> */}

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
        />

        {audioMode ? (
          <ChatAudioControls
            className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl"
            text={lastAssistantText}
            isListening={recordingState === "recording"}
            isProcessing={
              recordingState === "processing" ||
              status === "submitted" ||
              status === "streaming"
            }
            onStartListening={startRecording}
            onStopListening={stopRecording}
            onClose={() => setAudioMode(false)}
            status={status}
            useWebTTS={true} // Set to true to use Web TTS by default
          />
        ) : (
          <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
            {!isReadonly && (
              <MultimodalInput
                chatId={id}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmit}
                status={status}
                stop={stop}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
                selectedVisibilityType={visibilityType}
                onAudioModeToggle={() => setAudioMode(true)}
              />
            )}
          </form>
        )}
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />
    </>
  );
}

"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "./ui/button";
import { StopIcon, MicrophoneIcon, HeadphonesIcon } from "./icons";
import { useSpeechSynthesis } from "@/hooks/use-speech-synthesis";
import { useScrollToBottom } from "@/hooks/use-scroll-to-bottom";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface ChatAudioControlsProps {
  text: string;
  isListening: boolean;
  isProcessing: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  onClose: () => void;
  className?: string;
  status?: "ready" | "submitted" | "streaming" | "error";
  useWebTTS?: boolean; // New prop to choose between Web TTS and API TTS
}

export function ChatAudioControls({
  text,
  isListening,
  isProcessing,
  onStartListening,
  onStopListening,
  onClose,
  className,
  status = "ready",
  useWebTTS = true, // Default to Web TTS
}: ChatAudioControlsProps) {
  console.log(
    "ChatAudioControls rendered with text:",
    text ? text.substring(0, 100) + "..." : "null",
    "Using TTS method:",
    useWebTTS ? "Web TTS" : "API TTS"
  );
  const { speakWithAPI, cancel, state, isSupported } = useSpeechSynthesis({
    useWebTTS, // Pass the prop to the hook
    onEnd: () => {
      // Auto-start listening when speech ends
      if (!isListening && !isProcessing) {
        // Check if running on mobile device
        const urlParams = new URLSearchParams(window.location.search);
        const isMobile = urlParams.get("device") === "mobile";
        console.log("isMobile:", isMobile);
        if (isMobile) {
          // Add delay for mobile devices to prevent immediate recording restart
          setTimeout(() => {
            if (!isListening && !isProcessing) {
              onStartListening();
            }
          }, 2000); // 2 second delay for mobile
        } else {
          onStartListening();
        }
      }
    },
    onError: (error) => {
      console.error("Speech synthesis error:", error);
      toast.error(
        `Speech synthesis error: ${
          error instanceof Error ? error.message : error.error
        }`
      );
    },
  });

  // Add scroll functionality
  const { scrollToBottom } = useScrollToBottom();

  // Auto-scroll when status changes to submitted (when a message is sent)
  useEffect(() => {
    if (status === "submitted") {
      scrollToBottom();
    }
  }, [status, scrollToBottom]);

  // Auto-scroll when starting to listen (when user starts speaking)
  useEffect(() => {
    if (isListening) {
      scrollToBottom();
    }
  }, [isListening, scrollToBottom]);

  // Auto-scroll when AI starts speaking
  useEffect(() => {
    if (state === "speaking") {
      scrollToBottom();
    }
  }, [state, scrollToBottom]);

  const [autoPlay, setAutoPlay] = useState(true);
  const [isFirstMount, setIsFirstMount] = useState(true);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneStreamRef = useRef<MediaStream | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const lastSpokenTextRef = useRef<string>("");

  // Auto-play text when it changes (only once per unique text) or on first mount
  useEffect(() => {
    console.log("Auto-play effect triggered with:", {
      text: text ? text.substring(0, 50) + "..." : "null",
      autoPlay,
      isListening,
      isProcessing,
      state,
      isFirstMount,
      lastSpokenText: lastSpokenTextRef.current
        ? lastSpokenTextRef.current.substring(0, 50) + "..."
        : "null",
      textChanged: text !== lastSpokenTextRef.current,
    });

    if (
      text &&
      autoPlay &&
      !isListening &&
      !isProcessing &&
      state !== "speaking" &&
      (text !== lastSpokenTextRef.current || isFirstMount) // Play on first mount or text change
    ) {
      console.log("Auto-playing text:", text);
      lastSpokenTextRef.current = text; // Mark this text as spoken
      setIsFirstMount(false); // Clear first mount flag

      // Add a small delay to ensure the TTS API is ready
      // Use longer delay on first mount to ensure everything is initialized
      const delay = isFirstMount ? 800 : 300;
      const timer = setTimeout(async () => {
        try {
          // Use speakWithAPI() to summarize text first for TTS-friendly output
          await speakWithAPI(text);
        } catch (error) {
          console.error("Failed to speak with API:", error);
        }
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [
    text,
    autoPlay,
    isListening,
    isProcessing,
    state,
    isFirstMount,
    speakWithAPI,
  ]);

  // Debug text content
  useEffect(() => {
    if (text) {
      console.log("Text for TTS:", text);
      console.log("Text length:", text.length);
    }
  }, [text]);

  // Initialize audio context and analyzer
  useEffect(() => {
    if (typeof window !== "undefined") {
      audioContextRef.current = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
    }

    return () => {
      if (
        audioContextRef.current &&
        audioContextRef.current.state !== "closed"
      ) {
        audioContextRef.current.close();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (microphoneStreamRef.current) {
        microphoneStreamRef.current
          .getTracks()
          .forEach((track) => track.stop());
      }
    };
  }, []);

  // Setup microphone stream when listening
  useEffect(() => {
    if (isListening && !microphoneStreamRef.current) {
      const setupMicrophone = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });
          microphoneStreamRef.current = stream;

          if (audioContextRef.current && analyserRef.current) {
            sourceNodeRef.current =
              audioContextRef.current.createMediaStreamSource(stream);
            sourceNodeRef.current.connect(analyserRef.current);
            startVisualizer();
          }
        } catch (error) {
          console.error("Error accessing microphone:", error);
        }
      };

      setupMicrophone();
    } else if (!isListening && microphoneStreamRef.current) {
      // Clean up microphone stream when not listening
      microphoneStreamRef.current.getTracks().forEach((track) => track.stop());
      microphoneStreamRef.current = null;

      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect();
        sourceNodeRef.current = null;
      }
    }
  }, [isListening]);

  // Start/stop visualizer based on speaking or listening state
  useEffect(() => {
    if ((state === "speaking" || isListening) && canvasRef.current) {
      startVisualizer();
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }

      // Draw empty visualizer
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          drawEmptyBars(ctx, canvas.width, canvas.height);
        }
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state, isListening]);

  // Draw empty equalizer bars
  const drawEmptyBars = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    const barCount = 100;
    const barWidth = width / barCount;
    const barGap = 2;

    ctx.fillStyle = "#e2e8f0"; // Light gray for inactive bars

    for (let i = 0; i < barCount; i++) {
      const x = i * barWidth;
      const barHeight = 3; // Minimal height for inactive bars
      ctx.fillRect(
        x + barGap / 2,
        height - barHeight,
        barWidth - barGap,
        barHeight
      );
    }
  };

  // Start the visualizer animation
  const startVisualizer = () => {
    if (!canvasRef.current || !analyserRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      animationRef.current = requestAnimationFrame(draw);

      analyser.getByteFrequencyData(dataArray);

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const barCount = 100; // Number of bars to display
      const barWidth = canvas.width / barCount;
      const barGap = 2;

      // Generate some fake data if we're speaking but don't have real audio data
      const data =
        state === "speaking" && !isListening
          ? Array.from({ length: barCount }, () => Math.random() * 100 + 50)
          : Array.from(dataArray.slice(0, barCount));

      // Draw the bars
      for (let i = 0; i < barCount; i++) {
        const value = data[i];
        const percent = value / 255;
        const barHeight = percent * canvas.height;

        // Create gradient based on height
        let color;
        if (isListening) {
          color = `rgb(239, 68, 68, ${0.7 + percent * 0.3})`; // Red for listening
        } else {
          // Blue gradient for speaking
          color = `rgb(59, 130, 246, ${0.7 + percent * 0.3})`;
        }

        ctx.fillStyle = color;
        ctx.fillRect(
          i * barWidth + barGap / 2,
          canvas.height - barHeight,
          barWidth - barGap,
          barHeight
        );
      }
    };

    draw();
  };

  const closeAudio = () => {
    // Cancel any ongoing speech when closing
    cancel();
    onClose();
  };

  // Handle microphone controls
  const handleMicrophoneClick = () => {
    if (isListening) {
      onStopListening();
    } else {
      // Cancel any ongoing speech before starting to listen
      if (state === "speaking" || state === "paused") {
        cancel();
      }
      onStartListening();
    }
  };

  // Get button style based on state
  const getMicButtonStyle = () => {
    if (isListening) {
      return "bg-red-500 hover:bg-red-600 text-white";
    } else if (isProcessing) {
      return "bg-purple-500 hover:bg-purple-600 text-white";
    }
    return "bg-purple-500 hover:bg-purple-600 text-white";
  };

  if (!isSupported) {
    return (
      <div className={cn("flex flex-col items-center gap-4 p-4", className)}>
        <div className="text-red-500">
          Speech synthesis is not supported in this browser.
        </div>
        <Button onClick={closeAudio} variant="outline">
          Close Audio Mode
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col items-center gap-4", className)}>
      <div className="w-full rounded-lg border border-zinc-700 p-4">
        {/* Header */}
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <HeadphonesIcon size={16} />
            <span className="text-sm font-medium">Conversational Mode</span>
            <span className="text-xs text-muted-foreground">
              {state === "speaking"
                ? "Speaking..."
                : isListening
                ? "Listening..."
                : isProcessing
                ? "Processing..."
                : "Ready"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={closeAudio}
              variant="ghost"
              size="sm"
              className="h-8 px-2"
            >
              Close
            </Button>
          </div>
        </div>

        {/* Equalizer visualization */}
        <div className="w-full h-16 flex items-center justify-center">
          <canvas
            ref={canvasRef}
            className="w-full h-12 rounded-md"
            width={300}
            height={48}
          />
        </div>

        {/* Main controls */}
        <div className="flex items-center justify-center gap-4">
          {/* Microphone button */}
          <Button
            data-testid="chat-audio-microphone-button"
            className={`rounded-full p-3 h-16 w-16 ${
              isListening || state === "speaking" ? "animate-pulse" : ""
            } ${getMicButtonStyle()}`}
            onClick={handleMicrophoneClick}
            disabled={isProcessing}
            variant={isListening ? "default" : "outline"}
            aria-label={isListening ? "Stop listening" : "Start listening"}
          >
            {isListening ? (
              <StopIcon size={18} />
            ) : (
              <MicrophoneIcon size={18} />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

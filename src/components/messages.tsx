import type { UIMessage } from "ai";
import { PreviewMessage, ThinkingMessage } from "./message";
import { Greeting } from "./greeting";
import { memo } from "react";
import type { Vote } from "@/lib/db/schema";
import equal from "fast-deep-equal";
import type { UseChatHelpers } from "@ai-sdk/react";
import { motion } from "framer-motion";
import { useMessages } from "@/hooks/use-messages";
import { Card, CardContent } from "@/components/ui/card";
import {
  Contact2Icon,
  ContactIcon,
  ContactRoundIcon,
  Phone,
} from "lucide-react";

interface MessagesProps {
  chatId: string;
  status: UseChatHelpers["status"];
  votes: Array<Vote> | undefined;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
  isArtifactVisible: boolean;
}

function PureMessages({
  chatId,
  status,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
}: MessagesProps) {
  const {
    containerRef: messagesContainerRef,
    endRef: messagesEndRef,
    onViewportEnter,
    onViewportLeave,
    hasSentMessage,
  } = useMessages({
    chatId,
    status,
  });

  return (
    <div
      ref={messagesContainerRef}
      className="flex flex-col min-w-0 gap-6 flex-1 overflow-y-scroll pt-4 relative"
    >
      {messages.length === 0 && <Greeting />}

      {messages.map((message, index) => {
        if (index === 0) {
          return <ContactCard key={message.id} message={message} />;
        }
        return (
          <PreviewMessage
            key={message.id}
            chatId={chatId}
            message={message}
            isLoading={status === "streaming" && messages.length - 1 === index}
            vote={
              votes
                ? votes.find((vote) => vote.messageId === message.id)
                : undefined
            }
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            requiresScrollPadding={
              hasSentMessage && index === messages.length - 1
            }
          />
        );
      })}

      {status === "submitted" &&
        messages.length > 0 &&
        messages[messages.length - 1].role === "user" && <ThinkingMessage />}

      <motion.div
        ref={messagesEndRef}
        className="shrink-0 min-w-[24px] min-h-[24px]"
        onViewportLeave={onViewportLeave}
        onViewportEnter={onViewportEnter}
      />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isArtifactVisible && nextProps.isArtifactVisible) return true;

  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.status && nextProps.status) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;

  return true;
});

export default function ContactCard({ message }: any) {
  const extractInfo = (content: string) => {
    const customerNameMatch = content.match(
      /\*\*Customer Name:\*\*\s*(.+?)\s*(\n|$)/
    );
    const mobileNumberMatch = content.match(
      /\*\*Mobile Number:\*\*\s*(.+?)\s*(\n|$)/
    );
    const villageNameMatch = content.match(
      /\*\*Village Name:\*\*\s*(.+?)\s*(\n|$)/
    );
    const activityDateMatch = content.match(/\*\*Activity Date:\*\*\s(.+)/);

    return {
      customerName: customerNameMatch ? customerNameMatch[1].trim() : "N/A",
      mobileNumber: mobileNumberMatch ? mobileNumberMatch[1].trim() : "N/A",
      villageName: villageNameMatch ? villageNameMatch[1].trim() : "N/A",
      activityDate: activityDateMatch ? activityDateMatch[1].trim() : "N/A",
    };
  };

  const { customerName, mobileNumber, villageName, activityDate } = extractInfo(
    message.content
  );

  return (
    <Card className="w-full max-w-md mx-auto my-6 rounded-2xl shadow-lg border border-gray-200">
      <CardContent className="flex items-start p-6 gap-4">
        <div className="flex-shrink-0">
          <div className="bg-blue-100 p-3 rounded-full">
            <ContactIcon className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        <div className="flex-grow space-y-1">
          <h2 className="text-xl font-bold text-gray-900">{customerName}</h2>
          <div className="text-sm text-gray-700">
            <p>
              <span className="font-medium">📞</span> {mobileNumber}
            </p>
            <p>
              <span className="font-medium">🏠</span> {villageName}
            </p>
            <p>
              <span className="font-medium">📅</span> {activityDate}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
// import { Badge } from "@/components/ui/badge"
import { Pill } from "lucide-react";

interface MedItem {
  id: number;
  name: string;
  score: number;
}

interface MedListProps {
  medications: any[];
}

export default function MedList({ medications }: MedListProps) {
  console.log("medications", medications);
  const formatScore = (score: number) => {
    return `${(score * 100).toFixed(1)}%`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.5) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 0.3) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Medication List
        </h2>
        <p className="text-gray-600">Found {medications.length} medications</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {medications.map((med) => (
          <Card
            key={med.id}
            className="hover:shadow-lg transition-shadow duration-200"
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Pill className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-sm font-medium text-gray-500">
                    ID: {med.id}
                  </CardTitle>
                </div>
                {/* <Badge variant="outline" className={`${getScoreColor(med.score)} font-medium`}>
                  {formatScore(med.score)}
                </Badge> */}
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <h3 className="font-semibold text-lg text-gray-900 leading-tight">
                {med.name}
              </h3>

              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">Match Score</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${med.score * 100}%` }}
                      />
                    </div>
                    <span className="font-medium text-gray-700">
                      {formatScore(med.score)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {medications.length === 0 && (
        <div className="text-center py-12">
          <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No medications found
          </h3>
          <p className="text-gray-500">
            No medication data available to display.
          </p>
        </div>
      )}
    </div>
  );
}

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface Prediction {
  confidence_percent: number;
  medicine: string;
}

interface MedicationData {
  found: boolean;
  message: string;
  matches: {
    predictions: Prediction[];
  };
}

interface MedicationOptionsProps {
  medicationData: MedicationData;
}

export default function MedicationOptions({
  medicationData,
}: MedicationOptionsProps) {
  if (!medicationData.found) {
    return (
      <div className="text-center py-12 text-gray-600">
        <h3 className="text-lg font-medium">No medications found</h3>
        <p className="text-sm mt-1">{medicationData.message}</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      <Card className="shadow-sm border">
        <CardHeader>
          <CardTitle className="text-lg">Medication List</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {medicationData.matches.predictions.length > 0 ? (
            medicationData.matches.predictions.map((prediction, index) => (
              <div
                key={index}
                className="p-3 rounded-md bg-gray-50 border text-gray-800"
              >
                {prediction.medicine}
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500 text-center">
              No predictions available
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

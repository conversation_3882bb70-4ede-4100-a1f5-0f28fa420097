"use client";
import Link from "next/link";
import { memo } from "react";
import ReactMarkdown, { type Components } from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { PrescriptionSummary } from "./prescription-summary";
import { CodeBlockV2 } from "./code-block-v2";
import { MedicineListWithConfidence } from "./medicine-chip";
import DiagnosisListChip from "./diagnosis-chip";

function containsDiagnosisSelection(content: string): boolean {
  return content.startsWith("Diagnosis Recorded:");
}

function parseDiagnosisList(
  content: string
): { name: string; reference: string }[] {
  const lines = content.split("\n");
  return lines
    .filter((line) => line.includes("(Reference:"))
    .map((line) => {
      const match = line.match(/^- (.+?) \(Reference: (\d+)\)/);
      if (!match) return null;
      const [, name, reference] = match;
      return { name: name.trim(), reference: reference.trim() };
    })
    .filter(Boolean) as { name: string; reference: string }[];
}

function containsMedicineSelection(content: string): boolean {
  return content.includes("**Choose the medicines you'd like to prescribe");
}

function parseMedicinesWithConfidence(
  content: string
): { name: string; confidence: number }[] {
  const lines = content.split("\n").filter((line) => line.trim());
  const medicineLines = lines.filter((line) => /^\d+\.\s/.test(line));
  const confidenceMap = new Map<string, number>();

  // Extract medicine lines like "1. CADISTIN INJ 100 ML"
  const medicines = medicineLines.map((line) => {
    const match = line.match(/^\d+\.\s(.+)$/);
    return match ? match[1].trim() : "";
  });

  // Try to extract confidence from other parts of content if available (if not, mock example used)
  // For your case, you already have `toolInvocation` that includes confidence, so you might want to pass that separately.

  // For now, we’ll just use a fallback like 10.2% as per your example.
  return medicines.map((name) => ({
    name,
    confidence: 10.2, // fallback — ideally, get it from structured data if possible
  }));
}

// Function to detect if content contains prescription summary
function containsPrescriptionSummary(content: string): boolean {
  return content.includes("Patient Details:");
}

// Function to detect if content contains veterinary prescription HTML
function containsVetHTML(content: string): boolean {
  return (
    content.includes('class="inline-block px-3 py-1 rounded-full') ||
    content.includes("<strong>Symptoms</strong>") ||
    content.includes("<strong>Temperature</strong>") ||
    content.includes("<strong>Weight</strong>")
  );
}

// Function to detect if content contains veterinary prescription HTML

// Parse prescription summary from text
function parsePrescriptionSummary(content: string) {
  const lines = content.split("\n");
  const data = {
    patientDetails: {
      customerName: extractValue(lines, "Customer Name:"),
      animalName: extractValue(lines, "Animal Name:"),
      animalType: extractValue(lines, "Animal Type:"),
      careCalendarId: extractValue(lines, "Care Calendar ID:"),
      mobileNumber: extractValue(lines, "Mobile Number:"),
      villageName: extractValue(lines, "Village Name:"),
      animalId: extractValue(lines, "Animal ID:"),
      activityStatus: extractValue(lines, "Activity Status:"),
    },
    caseDescription: {
      symptoms: extractValue(lines, "Symptoms:"),
      temperature: extractValue(lines, "Temperature:"),
      weight: extractValue(lines, "Weight:"),
    },
    diagnosis: extractDiagnosis(lines),
    medications: extractMedications(lines),
    advisoryNotes:
      extractValue(lines, "Advisory Notes:") || "Drink plenty of water.",
  };
  return data;
}

function extractValue(lines: string[], key: string): string {
  const line = lines.find((l) => l.includes(key));
  return line ? line.split(key)[1]?.trim() || "" : "";
}

function extractDiagnosis(
  lines: string[]
): { name: string; reference: string }[] {
  const diagnosisLine = extractValue(lines, "Diagnosis:");
  if (!diagnosisLine) return [];

  const match = diagnosisLine.match(/^(.*?)\s*\(Reference:\s*(\d+)\)/);
  if (!match) return [];

  return [
    {
      name: match[1].trim(),
      reference: match[2].trim(),
    },
  ];
}

function extractMedications(lines: string[]): {
  name: string;
  reference: string;
  route: string;
  dosage: string;
  frequency: string;
}[] {
  const nameLine = extractValue(lines, "Medications:");
  const routeLine = extractValue(lines, "Route:");
  const dosageLine = extractValue(lines, "Dosage:");
  const frequencyLine = extractValue(lines, "Frequency:");

  if (!nameLine) return [];

  const match = nameLine.match(/^(.*?)\s*\(Ref:\s*(\d+)\)/);
  if (!match) return [];

  return [
    {
      name: match[1].trim(),
      reference: match[2].trim(),
      route: routeLine ?? "",
      dosage: dosageLine ?? "",
      frequency: frequencyLine ?? "",
    },
  ];
}

// HTML Parser component for veterinary content
function VetHTMLParser({ content }: { content: string }) {
  const processedContent = content.replace(
    /<div class="inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900 bg-blue-400">(.*?)<\/div>/g,
    '<span class="inline-block w-fit px-3 py-1 rounded-full text-sm font-medium text-gray-900 bg-yellow-200 ml-2">$1</span>'
  );
  return (
    <div
      className="max-w-none"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

const components: Partial<Components> = {
  // @ts-expect-error
  code: CodeBlockV2,
  pre: ({ children }) => <>{children}</>,
  ol: ({ node, children, ...props }) => {
    return (
      <ol className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ol>
    );
  },
  li: ({ node, children, ...props }) => {
    return (
      <li className="py-1" {...props}>
        {children}
      </li>
    );
  },
  ul: ({ node, children, ...props }) => {
    return (
      <ul className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ul>
    );
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    );
  },
  a: ({ node, children, ...props }) => {
    return (
      // @ts-expect-error
      <Link
        className="text-blue-500 hover:underline"
        target="_blank"
        rel="noreferrer"
        {...props}
      >
        {children}
      </Link>
    );
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1 className="text-3xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h1>
    );
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2 className="text-2xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h2>
    );
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3 className="text-xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h3>
    );
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4 className="text-lg font-semibold mt-6 mb-2" {...props}>
        {children}
      </h4>
    );
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5 className="text-base font-semibold mt-6 mb-2" {...props}>
        {children}
      </h5>
    );
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6 className="text-sm font-semibold mt-6 mb-2" {...props}>
        {children}
      </h6>
    );
  },
};

const remarkPlugins = [remarkGfm];
const rehypePlugins = [rehypeRaw];

const NonMemoizedMarkdown = ({ children }: { children: string }) => {
  if (containsPrescriptionSummary(children)) {
    const summaryData = parsePrescriptionSummary(children);
    // return <PrescriptionSummary data={summaryData} />;
    return null;
  }

  if (containsVetHTML(children)) {
    return <VetHTMLParser content={children} />;
  }

  // if (containsDiagnosisSelection(children)) {
  //   const parsed = parseDiagnosisList(children);
  //   // return <DiagnosisListChip diagnoses={parsed} />;
  //   return null;
  // }

  // if (containsMedicineSelection(children)) {
  //   const medicines = parseMedicinesWithConfidence(children);
  //   // return <MedicineListWithConfidence medicines={medicines} />;
  //   return null;
  // }

  return (
    <ReactMarkdown
      remarkPlugins={remarkPlugins}
      rehypePlugins={rehypePlugins}
      components={components}
    >
      {children}
    </ReactMarkdown>
  );
};

export const Markdown = memo(
  NonMemoizedMarkdown,
  (prevProps, nextProps) => prevProps.children === nextProps.children
);

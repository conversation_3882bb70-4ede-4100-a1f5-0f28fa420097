'use client";';
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import axios from "axios";

interface DownloadPrescriptionProps {
  data: any;
}

const DownloadPrescription: React.FC<DownloadPrescriptionProps> = ({
  data,
}) => {
  //get url from data
  const { farmerId, careCalendarId } = data?.data;

  const [loading, setLoading] = useState(false);

  const handleGenerateRX = async () => {
    setLoading(true);

    const baseUrl =
      localStorage.getItem("baseUrl") || process.env.NEXT_PUBLIC_BASE_URL;

    const url = `${baseUrl}/v2/tasks/rx/pdf?language=en&farmer_id=${farmerId}&calendar_id=${careCalendarId}`;

    const token =
      localStorage.getItem("token") || process.env.NEXT_PUBLIC_TOKEN;
    try {
      const response = await axios({
        //use that url here
        url: url,
        method: "GET",
        headers: {
          Token: token || "",
        },
        responseType: "arraybuffer",
      });

      const pdfBlob = new Blob([response.data], { type: "application/pdf" });

      const pdfUrl = URL.createObjectURL(pdfBlob);
      window.open(pdfUrl, "_blank");

      setLoading(false);
    } catch (error) {
      console.error("Error generating PDF:", error);
      setLoading(false);
    }
  };
  return (
    <div className="mt-4">
      <p className="text-gray-600 mb-4">
        Click the button below to download the prescription as a PDF.
      </p>
      <Button
        onClick={handleGenerateRX}
        className="flex items-center gap-2 bg-purple-700 hover:bg-purple-800 text-white font-medium py-2 px-4 rounded-md transition-colors"
      >
        <FileText className="h-5 w-5" />
        {loading ? "Generating PDF..." : "Download Prescription"}
      </Button>
    </div>
  );
};

export default DownloadPrescription;

"use client";

import { useMemo } from "react";

interface HTMLParserProps {
  content: string;
}

export function HTMLParser({ content }: HTMLParserProps) {
  const parsedContent = useMemo(() => {
    // Replace HTML with React components
    let parsed = content;

    // Replace symptom divs with styled spans
    parsed = parsed.replace(
      /<div class="inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900 bg-blue-400">(.*?)<\/div>/g,
      '<span className="inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900 bg-blue-400">$1</span>'
    );

    return parsed;
  }, [content]);

  return (
    <div
      dangerouslySetInnerHTML={{ __html: parsedContent }}
      className="prose prose-sm max-w-none"
    />
  );
}

import { Activity, AlertCircle } from "lucide-react";

interface Symptom {
  id: number;
  name: string;
  score: number;
}

interface DiagnosisListProps {
  symptoms: Symptom[];
}

export default function DiagnosisList({ symptoms }: DiagnosisListProps) {
  console.log("symptoms", symptoms);
  return (
    <div className="max-w-2xl mx-auto bg-white">
      <div className="text-lg font-semibold text-gray-800 mb-3">
        Diagnosis List (Please Select One or More)
      </div>
      <div className="space-y-2">
        {symptoms.map((symptom, index) => (
          <div
            key={symptom.id}
            className="flex items-center gap-3 py-2 hover:bg-gray-50 rounded-lg px-2 transition-colors duration-150"
          >
            <div className="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full text-sm font-medium text-gray-600">
              {index + 1}
            </div>
            <SymptomChip symptom={symptom.name || ""} />
          </div>
        ))}
      </div>
    </div>
  );
}

function SymptomChip({ symptom }: { symptom: string }) {
  const bgColor = "bg-yellow-200";

  return (
    <div
      className="inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900"
      style={{ backgroundColor: bgColor }}
    >
      {symptom}
    </div>
  );
}

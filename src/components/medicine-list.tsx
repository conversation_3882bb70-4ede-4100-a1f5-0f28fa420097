import { Activity, AlertCircle } from "lucide-react";

interface Medicine {
  confidence_percent: number;
  medicine: string;
  type: string; // optional, if you plan to show it later
}

interface DiagnosisListProps {
  medicines: Medicine[];
}

const getBackgroundColor = (type: string) => {
  switch (type) {
    case "protocol":
      return "bg-yellow-200";
    case "hybrid":
      return "bg-blue-200";
    case "best":
      return "bg-green-200";
    default:
      return "bg-gray-200";
  }
};

export default function MedicinesList({ medicines }: DiagnosisListProps) {
  return (
    <div className="max-w-2xl mx-auto bg-white">
      <div className="text-lg font-semibold text-gray-800 mb-3">
        Recommended Medicines (Please Select One or More)
      </div>

      <div className="space-y-2">
        {medicines.map((medicine, index) => (
          <div
            key={index}
            className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-lg transition-colors duration-150"
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-full text-sm font-medium text-gray-600">
                {index + 1}
              </div>
              <MedicineChip
                medicine={medicine.medicine || ""}
                type={medicine.type}
              />
            </div>

            <div className="text-sm text-blue-600 font-medium flex items-center gap-2">
              {medicine.confidence_percent}%{" "}
              <span className="text-gray-500 font-normal">confidence</span>
              <span
                className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium text-gray-900 ${getBackgroundColor(
                  medicine.type
                )}`}
              >
                {medicine.type}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Utility to generate random light colors
// const getRandomLightColor = () => {
//   const hue = Math.floor(Math.random() * 360);
//   return `hsl(${hue}, 100%, 90%)`;
// };

// Medicine chip UI
function MedicineChip({ medicine, type }: { medicine: string; type: string }) {
  // Function to get background color based on type

  return (
    <div
      className={`inline-block px-3 py-1 rounded-full text-sm font-medium text-gray-900 ${getBackgroundColor(
        type
      )}`}
    >
      {medicine}
    </div>
  );
}

import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";

interface PatientDetails {
  customerName: string;
  animalName: string;
  animalType: string;
  careCalendarId: string;
  mobileNumber: string;
  villageName: string;
  animalId: string;
  activityStatus: string;
}

interface CaseDescription {
  symptoms: string;
  temperature: string;
  weight: string;
}

interface DiagnosisItem {
  name: string;
  reference: string;
}

interface MedicationItem {
  name: string;
  reference: string;
  route: string;
  dosage: string;
  frequency: string;
}

interface PatientCaseProps {
  patientDetails: PatientDetails;
  caseDescription: CaseDescription;
  diagnosis: DiagnosisItem[];
  medications: MedicationItem[];
  followup: string | null;
  advisoryNotes: string;
}

export default function PatientCaseDetails({
  patientDetails,
  caseDescription,
  diagnosis,
  medications,
  followup,
  advisoryNotes,
}: PatientCaseProps) {
  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Patient Details Card */}
        <Card className="bg-blue-50 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">
              Patient Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {[
              ["Customer Name", patientDetails.customerName],
              ["Animal Name", patientDetails.animalName],
              ["Animal Type", patientDetails.animalType],
              ["Mobile Number", patientDetails.mobileNumber],
              ["Village", patientDetails.villageName],
              ["Activity Status", patientDetails.activityStatus],
              ["Animal ID", patientDetails.animalId],
              ["Care Calendar ID", patientDetails.careCalendarId],
            ].map(([label, value], idx) => (
              <div key={idx} className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">
                  {label}
                </div>
                <div className="text-sm font-medium break-words">{value}</div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Case Description Card */}
        <Card className="bg-green-50 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">
              Case Description
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {[
              ["Symptoms", caseDescription.symptoms],
              ["Temperature", caseDescription.temperature],
              ["Weight", caseDescription.weight],
            ].map(([label, value], idx) => (
              <div key={idx} className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">
                  {label}
                </div>
                <div className="text-sm font-medium break-words">{value}</div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Diagnosis Card */}
        <Card className="bg-yellow-50 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Diagnosis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {diagnosis.length > 0 ? (
              diagnosis.map((item, index) => (
                <div key={index} className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    {item.name}
                  </div>
                  <div className="text-sm font-medium break-words">
                    {item.reference}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground text-sm">
                No diagnosis recorded.
              </p>
            )}
          </CardContent>
        </Card>

        {/* Medications Card */}
        <Card className="bg-purple-50 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Medications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {medications.length > 0 ? (
              medications.map((item, index) => (
                <div
                  key={index}
                  className="space-y-2 p-3 bg-white/50 rounded-lg border-l-4 border-purple-200"
                >
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Name
                    </div>
                    <div className="text-sm font-medium break-words">
                      {item.name}
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-muted-foreground">
                        Route
                      </div>
                      <div className="text-sm break-words">{item.route}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-muted-foreground">
                        Dosage
                      </div>
                      <div className="text-sm break-words">{item.dosage}</div>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Frequency
                    </div>
                    <div className="text-sm break-words">{item.frequency}</div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground text-sm">
                No medications recorded.
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card className="bg-red-50 shadow-md">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Additional Notes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">
              Follow-up
            </div>
            <p className="text-sm break-words">
              {followup || "No follow-up scheduled."}
            </p>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">
              Advisory Notes
            </div>
            <p className="text-sm break-words">
              {advisoryNotes || "No advisory notes."}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-sky-50 border-sky-200 shadow-md">
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-2xl">✅</span>
              <span className="text-lg font-semibold text-sky-900">
                Final Check
              </span>
            </div>
            <p className="text-sky-800 leading-relaxed">
              Please review the details above.{" "}
              <strong>Is all the information correct?</strong> You can go back
              to modify the details or proceed to generate the prescription.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

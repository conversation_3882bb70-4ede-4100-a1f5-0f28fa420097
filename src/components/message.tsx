"use client";

import { tool, type UIMessage } from "ai";
import cx from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useState } from "react";
import type { Vote } from "@/lib/db/schema";
import {
  UserDarkIcon,
  CowLaughingIcon,
  BotIcon,
  HumanIcon,
  CowThinkingIcon,
  VeterinaryDoctorIcon,
} from "./icons";
import { Markdown } from "./markdown";
import { MessageActions } from "./message-actions";
import { PreviewAttachment } from "./preview-attachment";
import { Weather } from "./weather";
import equal from "fast-deep-equal";
import { cn, sanitizeText } from "@/lib/utils";
import { Button } from "./ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { MessageEditor } from "./message-editor";
import { MessageReasoning } from "./message-reasoning";
import type { UseChatHelpers } from "@ai-sdk/react";

import DownloadPrescription from "./download-prescription";
import MedList from "./med-list";
import MedicationOptions from "./med-options-card";
import DiagnosisList from "./diagnosis-list";
import MedicinesList from "./medicine-list";
import PatientCaseDetails from "./prescription-summary-display";

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  requiresScrollPadding,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
  requiresScrollPadding: boolean;
}) => {
  const [mode, setMode] = useState<"view" | "edit">("view");

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            "flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",
            {
              "w-full": mode === "edit",
              "group-data-[role=user]/message:w-fit": mode !== "edit",
            }
          )}
        >
          {message.role === "assistant" && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
              <div className="translate-y-px">
                <BotIcon />
              </div>
            </div>
          )}

          <div
            className={cn("flex flex-col gap-4 w-full", {
              "min-h-96": message.role === "assistant" && requiresScrollPadding,
            })}
          >
            {message.experimental_attachments &&
              message.experimental_attachments.length > 0 && (
                <div
                  data-testid={`message-attachments`}
                  className="flex flex-row justify-end gap-2"
                >
                  {message.experimental_attachments.map((attachment) => (
                    <PreviewAttachment
                      key={attachment.url}
                      attachment={attachment}
                    />
                  ))}
                </div>
              )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === "text") {
                // Check if this message has a medicineRecommendation tool result
                const hasMedicineRecommendationResult = message.parts?.some(
                  (p) =>
                    p.type === "tool-invocation" &&
                    (p.toolInvocation?.toolName === "medicineRecommendation" ||
                      p.toolInvocation?.toolName === "checkDiagnosis") &&
                    p.toolInvocation?.state === "result"
                );

                const hasPrescriptionSummaryResult = message.parts?.some(
                  (p) =>
                    p.type === "tool-invocation" &&
                    p.toolInvocation?.toolName === "prescriptionSummaryTool" &&
                    p.toolInvocation?.state === "result"
                );

                // If there's a medicineRecommendation result, don't render this text
                if (hasMedicineRecommendationResult) {
                  return null;
                }

                if (hasPrescriptionSummaryResult) return null;

                if (mode === "view") {
                  const isUser = message.role === "user";
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === "user" && !isReadonly && (
                        <div className="h-10 w-10 flex justify-center items-center">
                          <VeterinaryDoctorIcon />
                        </div>
                      )}

                      <motion.div
                        data-testid="message-content"
                        initial={{
                          opacity: 0,
                          scale: 0.8,
                          x: isUser ? 100 : -100,
                          rotate: isUser ? 10 : -10,
                        }}
                        animate={{
                          opacity: 1,
                          scale: 1,
                          x: 0,
                          rotate: 0,
                        }}
                        transition={{
                          type: "spring",
                          stiffness: 500,
                          damping: 30,
                          duration: 0.6,
                        }}
                        className={cn(
                          "flex flex-col w-full text-sm sm:text-base px-4 py-2 rounded-2xl shadow-md",
                          {
                            "self-end bg-blue-600 text-white rounded-br-sm":
                              isUser,
                            "self-start bg-gray-100 text-gray-900 rounded-bl-sm":
                              !isUser,
                          }
                        )}
                      >
                        <Markdown>{sanitizeText(part.text)}</Markdown>
                      </motion.div>
                    </div>
                  );
                }

                if (mode === "edit") {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === "tool-invocation") {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                if (state === "call") {
                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ["getWeather"].includes(toolName),
                      })}
                    >
                      {toolName === "getWeather" ? <Weather /> : null}
                      {toolName === "getComplaintDetailsById"
                        ? "Calling getComplaintDetailsById tool..."
                        : null}
                      {toolName === "checkDiagnosis"
                        ? "Checking diagnosis..."
                        : null}

                      {toolName === "generatePrescription"
                        ? "Calling generatePrescription tool..."
                        : null}
                    </div>
                  );
                }

                if (state === "result") {
                  const { result } = toolInvocation;
                  switch (toolName) {
                    case "getComplaintDetailsById":
                      return (
                        <div key={toolCallId}>
                          {toolName === "getComplaintDetailsById" ? null : (
                            <pre>{JSON.stringify(result, null, 2)}</pre>
                          )}
                        </div>
                      );

                    case "checkDiagnosis":
                      return (
                        <div key={toolCallId}>
                          {toolName === "checkDiagnosis" ? (
                            <DiagnosisList symptoms={result?.matches} />
                          ) : null}
                        </div>
                      );

                    // case "checkDiagnosis":
                    //   return (
                    //     <div key={toolCallId}>
                    //       {toolName === "checkDiagnosis" ? null : (
                    //         <pre>{JSON.stringify(result, null, 2)}</pre>
                    //       )}
                    //     </div>
                    //   );

                    case "medicineRecommendation":
                      return (
                        <div key={toolCallId}>
                          {toolName === "medicineRecommendation" ? (
                            <MedicinesList
                              medicines={result?.matches?.predict || []}
                            />
                          ) : null}
                        </div>
                      );

                    // case "medicineRecommendation":
                    //   return (
                    //     <div key={toolCallId}>
                    //       {toolName === "medicineRecommendation" ? null : (
                    //         <pre>{JSON.stringify(result, null, 2)}</pre>
                    //       )}
                    //     </div>
                    //   );

                    // case "checkMedicine":
                    //   return (
                    //     <div key={toolCallId}>
                    //       {toolName === "checkMedicine" ? null : (
                    //         <pre>{JSON.stringify(result, null, 2)}</pre>
                    //       )}
                    //     </div>
                    //   );

                    case "prescriptionSummaryTool":
                      console.log("prescriptionSummaryTool", result);
                      return (
                        <div key={toolCallId}>
                          {toolName === "prescriptionSummaryTool" ? (
                            <PatientCaseDetails {...result?.result} />
                          ) : (
                            "Prescription is not generated"
                          )}
                        </div>
                      );

                    case "generatePrescription":
                      console.log("generatePrescription", result);
                      return (
                        <div key={toolCallId}>
                          {toolName === "generatePrescription" ? (
                            <DownloadPrescription data={{ ...result }} />
                          ) : (
                            "Prescription is not generated"
                          )}
                        </div>
                      );

                    // case "medicineRecommendation":
                    //   console.log("medicineRecommendation", result);
                    //   return (
                    //     <div key={toolCallId}>
                    //       {toolName === "medicineRecommendation" ? (
                    //         <MedicationOptions medicationData={{ ...result }} />
                    //       ) : (
                    //         "Prescription is not generated"
                    //       )}
                    //     </div>
                    //   );

                    default:
                      "No Tool Call to render";
                      break;
                  }
                }
              }
            })}

            {/* {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )} */}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding)
      return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;

    return true;
  }
);

export const ThinkingMessage = () => {
  const role = "assistant";

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message min-h-96"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          "flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
          {
            "group-data-[role=user]/message:bg-muted": true,
          }
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <BotIcon />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hang tight! I'm working on the treatment plan…
            {/* <div className="w-16 h-16">
              <CowThinkingIcon />
            </div> */}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const getRandomLightColor = () => {
  const hue = Math.floor(Math.random() * 360);
  return `hsl(${hue}, 100%, 90%)`;
};

interface Medicine {
  name: string;
  confidence: number;
}

export function MedicineListWithConfidence({
  medicines,
}: {
  medicines: Medicine[];
}) {
  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="text-lg font-semibold text-gray-800 mb-3">
        Recommended Medicines (Please Select On)
      </div>

      <div className="space-y-2">
        {medicines.map((medicine, index) => (
          <div
            key={index}
            className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-lg transition-colors duration-150"
          >
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-6 h-6 bg-gray-200 rounded-full text-xs font-bold text-gray-700">
                {index + 1}
              </div>
              <MedicineChip medicine={medicine.name} />
            </div>

            {/* <div className="text-sm text-blue-600 font-medium">
              {medicine.confidence}%
              <span className="ml-1 text-gray-500 font-normal">confidence</span>
            </div> */}
          </div>
        ))}
      </div>
    </div>
  );
}

function MedicineChip({ medicine }: { medicine: string }) {
  const bgColor = getRandomLightColor();

  return (
    <span
      className="px-3 py-1 rounded-full text-sm font-medium text-gray-900"
      style={{ backgroundColor: bgColor }}
    >
      {medicine}
    </span>
  );
}

"use client";

import { useState, useRef, useCallback, memo } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "./ui/button";
import { MicrophoneIcon, StopIcon } from "./icons";
import type { UseChatHelpers } from "@ai-sdk/react";

type RecordingState = "idle" | "recording" | "processing";

interface MicrophoneButtonProps {
  setInput: UseChatHelpers["setInput"];
  submitForm: () => void;
  status: UseChatHelpers["status"];
}

function PureMicrophoneButton({
  setInput,
  submitForm,
  status,
}: MicrophoneButtonProps) {
  const [recordingState, setRecordingState] = useState<RecordingState>("idle");
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const startRecording = useCallback(async () => {
    console.log("Starting recording...");
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          console.log("Audio data available, chunk size:", event.data.size);
        }
      };

      mediaRecorder.onstop = async () => {
        console.log("MediaRecorder onstop event triggered");
        setRecordingState("processing");

        try {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: "audio/webm",
          });

          // Create a FormData object to send the audio file
          const formData = new FormData();
          formData.append("file", audioBlob, "recording.webm");

          // Send the audio to the speech-to-text API
          const response = await fetch("/api/speech-to-text", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to transcribe audio");
          }

          const data = await response.json();

          // Set the transcribed text as input
          if (data.text || data.transcript) {
            const transcribedText = data.text || data.transcript;
            console.log("Transcribed text:", transcribedText);

            // Set the input and ensure form submission happens after state update
            setInput(transcribedText);

            // Use a slightly longer timeout to ensure state is updated
            setTimeout(() => {
              console.log("Submitting form with text:", transcribedText);
              submitForm();
            }, 300);
          } else {
            toast.error("No speech detected. Please try again.");
          }
        } catch (error) {
          console.error("Error processing audio:", error);
          toast.error("Failed to process audio. Please try again.");
        } finally {
          setRecordingState("idle");

          // Stop all tracks in the stream to release the microphone
          if (mediaRecorderRef.current) {
            const tracks = (
              mediaRecorderRef.current.stream as MediaStream
            ).getTracks();
            tracks.forEach((track) => track.stop());
          }
        }
      };

      mediaRecorder.start();
      setRecordingState("recording");
    } catch (error) {
      console.error("Error starting recording:", error);
      toast.error("Could not access microphone. Please check permissions.");
      setRecordingState("idle");
    }
  }, [setInput, submitForm]);

  const stopRecording = useCallback(() => {
    console.log("Stopping recording...");
    if (mediaRecorderRef.current && recordingState === "recording") {
      mediaRecorderRef.current.stop();
      console.log("MediaRecorder stopped");
    } else {
      console.log(
        "Cannot stop recording - state:",
        recordingState,
        "mediaRecorder:",
        !!mediaRecorderRef.current
      );
    }
  }, [recordingState]);

  const handleClick = useCallback(
    (event: React.MouseEvent) => {
      event.preventDefault();

      if (recordingState === "idle") {
        startRecording();
      } else if (recordingState === "recording") {
        stopRecording();
      }
    },
    [recordingState, startRecording, stopRecording]
  );

  // Determine button appearance based on recording state
  const getButtonStyle = () => {
    if (recordingState === "recording") {
      return "bg-red-500 hover:bg-red-600 text-white";
    } else if (recordingState === "processing") {
      return "bg-yellow-500 hover:bg-yellow-600 text-white";
    }
    return "dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200";
  };

  return (
    <Button
      data-testid="microphone-button"
      className={`rounded-md rounded-bl-lg p-[7px] h-fit ${getButtonStyle()}`}
      onClick={handleClick}
      disabled={status !== "ready" || recordingState === "processing"}
      variant={recordingState === "idle" ? "ghost" : "default"}
      title="Record message"
    >
      {recordingState === "recording" ? (
        <StopIcon size={14} />
      ) : (
        <MicrophoneIcon size={14} />
      )}
    </Button>
  );
}

export const MicrophoneButton = memo(PureMicrophoneButton);

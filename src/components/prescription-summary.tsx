"use client";

interface PrescriptionSummaryProps {
  data: {
    patientDetails: {
      customerName: string;
      animalName: string;
      animalType: string;
      careCalendarId: string;
      mobileNumber: string;
      villageName: string;
      animalId: string;
      activityStatus: string;
    };
    caseDescription: {
      symptoms: string;
      temperature: string;
      weight: string;
    };
    diagnosis: Array<{
      name: string;
      reference: string;
    }>;
    medications: Array<{
      name: string;
      reference: string;
      route: string;
      dosage: string;
      frequency: string;
    }>;
    advisoryNotes: string;
  };
}

export function PrescriptionSummary({ data }: PrescriptionSummaryProps) {
  return (
    <div className="max-w-4xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Prescription Summary
        </h2>
        <p className="text-gray-600">
          Complete summary of all collected information
        </p>
      </div>

      {/* Patient Details */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
          <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
          Patient Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">
              Customer Name:
            </span>
            <span className="text-gray-900">
              {data.patientDetails.customerName}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">Animal Name:</span>
            <span className="text-gray-900">
              {data.patientDetails.animalName}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">Animal Type:</span>
            <span className="text-gray-900">
              {data.patientDetails.animalType}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">
              Mobile Number:
            </span>
            <span className="text-gray-900">
              {data.patientDetails.mobileNumber}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">
              Village Name:
            </span>
            <span className="text-gray-900">
              {data.patientDetails.villageName}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-32">
              Activity Status:
            </span>
            <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
              {data.patientDetails.activityStatus}
            </span>
          </div>
        </div>
      </div>

      {/* Case Description */}
      <div className="bg-green-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-green-900 mb-3 flex items-center">
          <span className="w-2 h-2 bg-green-600 rounded-full mr-2"></span>
          Case Description
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center">
            <span className="font-medium text-gray-700 w-24">Symptoms:</span>
            <span className="inline-block px-3 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium ml-2">
              {data.caseDescription.symptoms}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-24">Temperature:</span>
            <span className="text-gray-900 font-medium">
              {data.caseDescription.temperature}
            </span>
          </div>
          <div className="flex">
            <span className="font-medium text-gray-700 w-24">Weight:</span>
            <span className="text-gray-900 font-medium">
              {data.caseDescription.weight}
            </span>
          </div>
        </div>
      </div>

      {/* Diagnosis */}
      <div className="bg-purple-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center">
          <span className="w-2 h-2 bg-purple-600 rounded-full mr-2"></span>
          Diagnosis
        </h3>
        <div className="space-y-2">
          {data.diagnosis.map((diag, index) => (
            <div key={index} className="flex items-center text-sm">
              <span className="inline-block px-3 py-1 bg-purple-100 text-purple-800 rounded-full font-medium">
                {diag.name}
              </span>
              <span className="text-gray-600 ml-2 text-xs">
                (Reference: {diag.reference})
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Medication */}
      <div className="bg-amber-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-amber-900 mb-3 flex items-center">
          <span className="w-2 h-2 bg-amber-600 rounded-full mr-2"></span>
          Medication
        </h3>
        <div className="space-y-4">
          {data.medications.map((med, index) => (
            <div
              key={index}
              className="bg-white rounded-lg p-3 border border-amber-200"
            >
              <div className="flex items-center mb-2">
                <span className="inline-block px-3 py-1 bg-amber-100 text-amber-800 rounded-full font-medium text-sm">
                  {med.name}
                </span>
                <span className="text-gray-600 ml-2 text-xs">
                  (Ref: {med.reference})
                </span>
              </div>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Route:</span>
                  <span className="block text-gray-900">{med.route}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Dosage:</span>
                  <span className="block text-gray-900">{med.dosage}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Frequency:</span>
                  <span className="block text-gray-900">{med.frequency}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Advisory Notes */}
      {data.advisoryNotes && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
            <span className="w-2 h-2 bg-gray-600 rounded-full mr-2"></span>
            Advisory Notes
          </h3>
          <p className="text-gray-700 text-sm">{data.advisoryNotes}</p>
        </div>
      )}

      {/* Confirmation */}
      <div className="border-t border-gray-200 pt-4">
        <p className="text-gray-900 font-medium">
          Is all this information correct and ready for prescription generation?
        </p>
      </div>
    </div>
  );
}

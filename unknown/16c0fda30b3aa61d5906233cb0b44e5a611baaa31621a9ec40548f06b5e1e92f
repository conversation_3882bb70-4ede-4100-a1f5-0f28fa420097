import React from "react";

interface PDFConfirmationProps {
  addToolResult: (result: { toolCallId: string; result: string }) => void;
  toolCallId: string;
}

const PDFConfirmation: React.FC<PDFConfirmationProps> = ({
  addToolResult,
  toolCallId,
}) => {
  return (
    <div className="mt-2">
      <h2 className="text-2xl font-semibold text-gray-800 mb-2">
        Download PDF?
      </h2>
      <p className="text-gray-600 mb-4">
        Are you sure you want to download the PDF document?
      </p>
      <div className="flex items-center gap-3">
        <button
          className="px-4 py-1.5 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 transition"
          onClick={() =>
            addToolResult({
              result: "Yes, confirmed.",
              toolCallId,
            })
          }
        >
          Yes
        </button>
        <button
          className="px-4 py-1.5 text-sm bg-red-500 text-white rounded-lg hover:bg-red-600 transition"
          onClick={() =>
            addToolResult({
              result: "No, denied.",
              toolCallId,
            })
          }
        >
          No
        </button>
      </div>
    </div>
  );
};

export default PDFConfirmation;
